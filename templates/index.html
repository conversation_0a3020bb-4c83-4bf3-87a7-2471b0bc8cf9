<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可研报告评审助手</title>
    <!-- 替换为本地CSS路径 -->
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link href="/static/all.min.css" rel="stylesheet">
    <style>
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .result-table {
            margin-top: 20px;
        }
        .criterion-card {
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .criterion-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .criterion-content {
            padding: 15px;
        }
        .result-badge {
            font-size: 0.9em;
            padding: 5px 10px;
        }
        .result-符合 { background-color: #d4edda; color: #155724; }
        .result-基本符合 { background-color: #fff3cd; color: #856404; }
        .result-不符合 { background-color: #f8d7da; color: #721c24; }
        .result-不适用 { background-color: #e2e3e5; color: #383d41; }
        .section-detail {
            font-size: 0.9em;
            margin-top: 10px;
        }
        .statistics-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .debug-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-log {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            white-space: pre-wrap;
            margin: 0;
        }
        .progress-item {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .progress-item:last-child {
            border-bottom: none;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">可研报告评审助手</h1>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <form id="uploadForm">
                            <div class="mb-3">
                                <label for="pdfFile" class="form-label">选择可研报告PDF文件</label>
                                <input type="file" class="form-control" id="pdfFile" accept=".pdf" required>
                            </div>
                            <button type="submit" class="btn btn-primary">开始评审</button>
                        </form>
                    </div>
                </div>

                <div class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在分析报告，请稍候...</p>
                </div>

                <!-- 调试面板 -->
                <div id="debugPanel" class="debug-panel" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0"><i class="fas fa-bug"></i> 评审过程调试信息 <span id="debugStatus" class="badge bg-secondary">离线</span></h6>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="clearDebugLog()">
                                <i class="fas fa-trash"></i> 清空
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleDebugPanel()">
                                <i class="fas fa-times"></i> 关闭
                            </button>
                        </div>
                    </div>
                    <div id="debugContent" class="debug-log"></div>
                </div>

                <div id="resultSection" class="result-table" style="display: none;">
                    <h3>评审结果</h3>

                    <!-- 评审服务计时统计区 -->
                    <div id="timingStatsCard" class="statistics-card">
                        <h5><i class="fas fa-clock"></i> 评审服务统计</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>执行时间</h6>
                                    <span id="executionTime" class="badge bg-primary fs-6">0秒</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>输入Token</h6>
                                    <span id="inputTokens" class="badge bg-info fs-6">0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>输出Token</h6>
                                    <span id="outputTokens" class="badge bg-warning fs-6">0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>总Token</h6>
                                    <span id="totalTokens" class="badge bg-secondary fs-6">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div id="statisticsCard" class="statistics-card">
                        <h5>评审统计</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>总审查细则</h6>
                                    <span id="totalCriteria" class="badge bg-primary fs-6">0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>符合</h6>
                                    <span id="compliantCount" class="badge bg-success fs-6">0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>不符合</h6>
                                    <span id="nonCompliantCount" class="badge bg-danger fs-6">0</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h6>合规率</h6>
                                    <span id="complianceRate" class="badge bg-info fs-6">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 审查细则列表 -->
                    <div id="criteriaList">
                    </div>

                    <div class="mt-4">
                        <h4>总体评审意见</h4>
                        <div id="summary" class="p-3 bg-light rounded"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // SSE连接和调试面板功能
        let eventSource = null;

        function initDebugStream() {
            return;
            if (eventSource) {
                eventSource.close();
            }

            eventSource = new EventSource('/debug-stream');

            eventSource.onopen = function() {
                updateDebugStatus('在线', 'success');
            };

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type !== 'heartbeat') {
                        addDebugLogFromServer(data);
                    }
                } catch (e) {
                    console.error('解析调试消息失败:', e);
                }
            };

            eventSource.onerror = function() {
                updateDebugStatus('离线', 'danger');
                // 3秒后重连
                setTimeout(initDebugStream, 3000);
            };
        }

        function updateDebugStatus(status, type) {
            const statusElement = document.getElementById('debugStatus');
            statusElement.textContent = status;
            statusElement.className = `badge bg-${type}`;
        }

        function toggleDebugPanel() {
            return;
            const panel = document.getElementById('debugPanel');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
                if (!eventSource) {
                    initDebugStream();
                }
            } else {
                panel.style.display = 'none';
            }
        }

        function clearDebugLog() {
            document.getElementById('debugContent').innerHTML = '';
        }

        function addDebugLog(message, type = 'info') {
            const debugContent = document.getElementById('debugContent');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'progress-item';

            let icon = '';
            switch(type) {
                case 'success': icon = '<i class="fas fa-check-circle text-success"></i>'; break;
                case 'error': icon = '<i class="fas fa-exclamation-circle text-danger"></i>'; break;
                case 'warning': icon = '<i class="fas fa-exclamation-triangle text-warning"></i>'; break;
                default: icon = '<i class="fas fa-info-circle text-info"></i>';
            }

            logEntry.innerHTML = `
                <span class="timestamp">[${timestamp}]</span>
                ${icon} ${message}
            `;

            debugContent.appendChild(logEntry);
            debugContent.scrollTop = debugContent.scrollHeight;
        }

        function addDebugLogFromServer(data) {
            const debugContent = document.getElementById('debugContent');
            const timestamp = new Date(data.timestamp * 1000).toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'progress-item';

            let icon = '';
            switch(data.level) {
                case 'success': icon = '<i class="fas fa-check-circle text-success"></i>'; break;
                case 'error': icon = '<i class="fas fa-exclamation-circle text-danger"></i>'; break;
                case 'warning': icon = '<i class="fas fa-exclamation-triangle text-warning"></i>'; break;
                default: icon = '<i class="fas fa-info-circle text-info"></i>';
            }

            logEntry.innerHTML = `
                <span class="timestamp">[${timestamp}]</span>
                ${icon} ${data.message}
            `;

            debugContent.appendChild(logEntry);
            debugContent.scrollTop = debugContent.scrollHeight;
        }

        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const fileInput = document.getElementById('pdfFile');
            const file = fileInput.files[0];
            if (!file) {
                alert('请选择PDF文件');
                return;
            }

            const formData = new FormData();
            formData.append('pdf_file', file);

            // 显示加载动画和调试面板
            document.querySelector('.loading').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            document.getElementById('debugPanel').style.display = 'none';

            // 重置计时统计信息
            document.getElementById('executionTime').textContent = '0秒';
            document.getElementById('inputTokens').textContent = '0';
            document.getElementById('outputTokens').textContent = '0';
            document.getElementById('totalTokens').textContent = '0';

            // 初始化调试流连接
            if (!eventSource) {
                initDebugStream();
            }

            // 清空调试日志
            document.getElementById('debugContent').innerHTML = '';

            // 添加初始调试信息
            addDebugLog(`开始分析文件: ${file.name}`, 'info');
            addDebugLog('正在上传文件...', 'info');

            try {
                addDebugLog('正在调用后端分析接口...', 'info');
                const response = await fetch('/analyze', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    addDebugLog(`HTTP错误: ${response.status} ${response.statusText}`, 'error');
                    throw new Error('评审失败');
                }

                addDebugLog('接收到分析结果，正在解析...', 'success');
                const result = await response.json();

                // 添加结果统计调试信息
                if (result.criteria_analysis) {
                    addDebugLog(`解析完成，共获得 ${result.criteria_analysis.length} 个审查细则的评审结果`, 'success');
                }
                if (result.statistics) {
                    const stats = result.statistics;
                    addDebugLog(`统计信息: 符合 ${(stats.result_distribution['符合'] || 0) + (stats.result_distribution['基本符合'] || 0)} 项, 不符合 ${stats.result_distribution['不符合'] || 0} 项`, 'info');
                }

                // 显示计时统计信息
                if (result.timing_stats) {
                    const timing = result.timing_stats;
                    document.getElementById('executionTime').textContent = timing.execution_time + '秒';
                    document.getElementById('inputTokens').textContent = timing.total_input_tokens || 0;
                    document.getElementById('outputTokens').textContent = timing.total_output_tokens || 0;
                    document.getElementById('totalTokens').textContent = timing.total_tokens || 0;
                    addDebugLog(`计时统计: 执行时间 ${timing.execution_time}秒, 总Token ${timing.total_tokens}`, 'info');
                }

                // 显示统计信息
                if (result.statistics) {
                    const stats = result.statistics;
                    document.getElementById('totalCriteria').textContent = result.criteria_analysis ? result.criteria_analysis.length : 0;
                    document.getElementById('compliantCount').textContent = (stats.result_distribution['符合'] || 0) + (stats.result_distribution['基本符合'] || 0);
                    document.getElementById('nonCompliantCount').textContent = stats.result_distribution['不符合'] || 0;
                    document.getElementById('complianceRate').textContent = stats.compliance_rate + '%';
                }

                // 显示审查细则结果
                const criteriaList = document.getElementById('criteriaList');
                criteriaList.innerHTML = '';

                if (result.criteria_analysis) {
                    console.log(`显示 ${result.criteria_analysis.length} 个审查细则`);
                    result.criteria_analysis.forEach((criterion, index) => {
                        console.log(`处理审查细则 ${index + 1}: ${criterion.criterion_id}`);
                        const criterionCard = document.createElement('div');
                        criterionCard.className = 'criterion-card';

                        // 综合分析内容
                        let comprehensiveAnalysis = '';
                        if (criterion.comprehensive_analysis) {
                            comprehensiveAnalysis = `
                                <div class="alert alert-info mb-3">
                                    <h6><i class="fas fa-chart-line"></i> 全文综合分析：</h6>
                                    <p class="mb-0">${criterion.comprehensive_analysis}</p>
                                </div>
                            `;
                        }

                        // 关键发现
                        let keyFindings = '';
                        if (criterion.key_findings && criterion.key_findings.length > 0) {
                            keyFindings = `
                                <div class="mb-3">
                                    <strong><i class="fas fa-search"></i> 关键发现：</strong>
                                    <ul class="list-unstyled mt-2">
                                        ${criterion.key_findings.map(finding => `
                                            <li class="mb-1"><i class="fas fa-dot-circle text-primary"></i> ${finding}</li>
                                        `).join('')}
                                    </ul>
                                </div>
                            `;
                        }

                        // 生成改进建议 - 优先使用新的recommendations
                        let suggestions = '';
                        const recommendations = criterion.recommendations || criterion.improvement_suggestions || [];
                        if (recommendations.length > 0) {
                            suggestions = `
                                <div class="mb-3">
                                    <strong><i class="fas fa-lightbulb"></i> 改进建议：</strong>
                                    <ul class="list-unstyled mt-2">
                                        ${recommendations.map(suggestion => `
                                            <li class="mb-1"><i class="fas fa-arrow-right text-success"></i> ${suggestion}</li>
                                        `).join('')}
                                    </ul>
                                </div>
                            `;
                        }

                        // 生成章节详情（折叠显示）
                        let sectionDetails = '';
                        // 优先使用新结构的sections字段，如果没有则使用旧的section_results字段
                        const sections = criterion.sections || criterion.section_results || [];
                        if (sections.length > 0) {
                            const relevantSections = sections.filter(s => s.result !== '不适用');
                            if (relevantSections.length > 0) {
                                const collapseId = `collapse-${criterion.criterion_id.replace(/\./g, '-')}`;
                                sectionDetails = `
                                    <div class="mt-3">
                                        <p>
                                            <a class="btn btn-outline-secondary btn-sm" data-bs-toggle="collapse" href="#${collapseId}" role="button" aria-expanded="false">
                                                <i class="fas fa-list"></i> 查看各章节详细评审情况 (${relevantSections.length}个相关章节)
                                            </a>
                                        </p>
                                        <div class="collapse" id="${collapseId}">
                                            <div class="card card-body">
                                                <ul class="list-unstyled">
                                                    ${relevantSections.map(section => `
                                                        <li class="mb-2">
                                                            <span class="badge result-badge result-${section.result}">${section.result}</span>
                                                            <strong>${section.section_name || section.section}</strong>
                                                            ${section.explanation && section.explanation !== 'N/A' ? `<br><small class="text-muted">${section.explanation}</small>` : ''}
                                                        </li>
                                                    `).join('')}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }
                        }

                        // 确定显示的评审结果 - 优先使用综合分析结果
                        const displayResult = criterion.overall_assessment || criterion.overall_result || '未知';

                        criterionCard.innerHTML = `
                            <div class="criterion-header">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">审查细则 ${criterion.criterion_id}</h6>
                                        <p class="mb-0 text-muted">${criterion.criterion_content}</p>
                                    </div>
                                    <span class="badge result-badge result-${displayResult}">${displayResult}</span>
                                </div>
                            </div>
                            <div class="criterion-content">
                                ${comprehensiveAnalysis}
                                ${keyFindings}
                                ${suggestions}
                                ${sectionDetails}
                            </div>
                        `;

                        criteriaList.appendChild(criterionCard);
                    });
                }

                // 显示总体评审意见
                let summaryContent = '';
                if (result.summary) {
                    if (typeof result.summary === 'string') {
                        summaryContent = result.summary.replace(/\n/g, '<br>');
                    } else if (result.summary.summary_text) {
                        summaryContent = result.summary.summary_text.replace(/\n/g, '<br>');
                    } else {
                        summaryContent = JSON.stringify(result.summary, null, 2);
                    }
                }
                document.getElementById('summary').innerHTML = summaryContent;

                addDebugLog('评审结果显示完成', 'success');
                document.getElementById('resultSection').style.display = 'block';
            } catch (error) {
                addDebugLog(`评审过程发生错误: ${error.message}`, 'error');
                alert('评审过程中发生错误：' + error.message);
            } finally {
                document.querySelector('.loading').style.display = 'none';
                addDebugLog('评审流程结束', 'info');
            }
        });
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>