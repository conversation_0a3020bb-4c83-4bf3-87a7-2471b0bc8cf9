import sys,os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.model_service import ModelService
from services.document_parser import DocumentParser
from services.report_analyzer import ReportAnalyzer
from dotenv import load_dotenv
load_dotenv()
import pymupdf,pymupdf4llm
def test_pdf_parse(pdf_path):
    md_text = pymupdf4llm.to_markdown(pdf_path)
    import pathlib
    pathlib.Path("1output.txt").write_bytes(md_text.encode())
def test_pdf_file(pdf_path):
    """测试单个PDF文件"""
    print(f"\n{'='*60}")    
    print(f"测试文件路径: {pdf_path}")
    print(f"{'='*60}")

    parser = DocumentParser()

    try:
        # 修改此处：接收两个返回值
        project_name, sections = parser.parse_pdf(pdf_path)
        print(f"解析成功，提取到 {len(sections)} 个章节")
        print(f"项目名称: {project_name}")  # 新增项目名称输出

        for section_title, content in sections.items():
            print(f"\n章节: {section_title}")
            print(f"内容预览(长度={len(content)}) : {content[:100]}...")

        # 统计有内容的章节数量
        non_empty_sections = sum(1 for content in sections.values() if content.strip())
        print(f"\n总结: 共{len(sections)}个章节，其中{non_empty_sections}个有内容")

        # 显示章节内容统计
        print("\n章节内容统计:")
        for section_title, content in sections.items():
            status = "✓ 有内容" if content.strip() else "✗ 无内容"
            char_count = len(content) if content.strip() else 0
            print(f"  {section_title}: {status} ({char_count}字符)")

        return sections

    except Exception as e:
        print(f"PDF 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数，用于执行所有测试用例"""
    # 测试两个PDF文件
    pdf_files = [
        #("uploads/1.广西电网横州市可行性研究报告.pdf", "广西电网横州市可行性研究报告"),
        ("templates/1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告.pdf")
    ]

    for pdf_path in pdf_files:
        if os.path.exists(pdf_path):
            test_pdf_file(pdf_path)
        else:
            print(f"\n文件不存在: {pdf_path}")

if __name__ == "__main__":
    main()

