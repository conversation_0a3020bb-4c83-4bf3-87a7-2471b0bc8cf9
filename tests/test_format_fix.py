#!/usr/bin/env python3
"""
测试格式化错误修复
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.model_service import ModelService

def test_comprehensive_analysis():
    """测试综合分析方法的格式化修复"""
    print("=== 测试综合分析方法格式化修复 ===")
    
    # 设置测试模式
    os.environ["TEST_MODE"] = "true"
    
    try:
        model_service = ModelService()
        
        # 模拟审查细则数据
        criterion = {
            "criterion_id": "1.1",
            "criterion_content": "测试审查细则内容",
            "section_results": [
                {
                    "section": "1 概述",
                    "result": "符合",
                    "explanation": "测试说明"
                }
            ]
        }
        
        # 模拟审查指南
        review_guide = "测试审查指南内容"
        
        # 模拟报告摘要
        report_summary = "测试报告摘要"
        
        print("调用综合分析方法...")
        result = model_service.analyze_criteria_comprehensive(
            review_guide=review_guide,
            criterion=criterion,
            report_summary=report_summary
        )
        
        print(f"综合分析结果: {result}")
        
        # 检查结果格式
        expected_keys = ["comprehensive_analysis", "overall_assessment", "key_findings", "recommendations"]
        for key in expected_keys:
            if key in result:
                print(f"✓ 包含字段: {key}")
            else:
                print(f"✗ 缺少字段: {key}")
                return False
        
        print("✓ 综合分析方法格式化修复成功")
        return True
        
    except Exception as e:
        print(f"✗ 综合分析方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_summarize_review():
    """测试汇总评审方法的格式化修复"""
    print("\n=== 测试汇总评审方法格式化修复 ===")
    
    # 设置测试模式
    os.environ["TEST_MODE"] = "true"
    
    try:
        model_service = ModelService()
        
        # 模拟评审结果数据
        review_results = [
            {
                "section": "1 概述",
                "has_content": True,
                "analysis": [
                    {
                        "criterion_id": "1.1",
                        "result": "符合",
                        "explanation": "测试说明1"
                    },
                    {
                        "criterion_id": "1.2",
                        "result": "不符合",
                        "explanation": "测试说明2"
                    }
                ]
            },
            {
                "section": "2 项目建设背景",
                "has_content": True,
                "analysis": [
                    {
                        "criterion_id": "2.1",
                        "result": "基本符合",
                        "explanation": "测试说明3"
                    }
                ]
            }
        ]
        
        print("调用汇总评审方法...")
        result = model_service.summarize_review(review_results)
        
        print(f"汇总评审结果: {result}")
        
        # 检查结果格式
        if "summary" in result:
            print("✓ 包含summary字段")
            if isinstance(result["summary"], str):
                print("✓ summary是字符串格式（测试模式）")
            elif isinstance(result["summary"], dict):
                print("✓ summary是字典格式（正常模式）")
            else:
                print(f"✗ summary格式异常: {type(result['summary'])}")
                return False
        else:
            print("✗ 缺少summary字段")
            return False
        
        print("✓ 汇总评审方法格式化修复成功")
        return True
        
    except Exception as e:
        print(f"✗ 汇总评审方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_string_formatting():
    """测试字符串格式化问题"""
    print("\n=== 测试字符串格式化问题 ===")
    
    try:
        # 测试包含双引号的字符串格式化
        test_string = '''
{
  "comprehensive_analysis": "对该审查细则的全文综合分析，包括整体评价和关键发现",
  "overall_assessment": "符合/基本符合/不符合/不适用",
  "key_findings": [
    "关键发现1",
    "关键发现2"
  ],
  "recommendations": [
    "具体改进建议1",
    "具体改进建议2"
  ]
}
'''
        
        # 测试字符串拼接（修复后的方法）
        review_guide = "测试审查指南"
        system_prompt = """
# 角色
你是专业的可研报告评审专家。

# 输出格式
请严格按照以下JSON格式输出：
``` + test_string + ```

# 审查指南：
""" + (review_guide if review_guide else '未提供审查指南')
        
        print("✓ 字符串拼接方法正常工作")
        print(f"系统提示词长度: {len(system_prompt)} 字符")
        
        # 测试f-string中的复杂格式化
        data = {
            'total_sections': 5,
            'sections_with_content': 3,
            'total_criteria': 10,
            'not_applicable_count': 2
        }
        
        # 使用修复后的方法
        non_compliance_list = "- 测试项目1\n- 测试项目2"
        compliance_list = "- 测试项目3\n- 测试项目4"
        
        prompt = f"""评审统计信息：
- 总章节数：{data['total_sections']}
- 有内容的章节数：{data['sections_with_content']}
- 总审查细则数：{data['total_criteria']}
- 不适用项目数：{data['not_applicable_count']}

不符合项目：
{non_compliance_list}

基本符合项目：
{compliance_list}
"""
        
        print("✓ 复杂f-string格式化正常工作")
        print(f"提示词长度: {len(prompt)} 字符")
        
        return True
        
    except Exception as e:
        print(f"✗ 字符串格式化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试格式化错误修复...\n")
    
    tests = [
        ("字符串格式化", test_string_formatting),
        ("综合分析方法", test_comprehensive_analysis),
        ("汇总评审方法", test_summarize_review)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{test_name}: {'✓ 通过' if result else '✗ 失败'}")
        except Exception as e:
            print(f"\n{test_name}: ✗ 异常 - {e}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有格式化错误修复测试通过！")
    else:
        print("⚠️  部分格式化错误修复测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
