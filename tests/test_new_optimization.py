#!/usr/bin/env python3
"""
测试报告评审新优化功能
1. 解析编制大纲的章节，当需要大模型评审某个章节时，只从编制大纲中提取该章节内容做为系统提示词
2. 增加一个审查指南，做为每个章节评审的提示词
3. 优化前端页面显示，增加一个调试区域可实时的输出报告评审的中间数据
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.document_parser import DocumentParser
from services.model_service import ModelService
from services.report_analyzer import ReportAnalyzer

def test_outline_parsing():
    """测试大纲解析功能"""
    print("=== 测试大纲解析功能 ===")
    
    parser = DocumentParser()
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    outline_path = os.path.join(current_dir, "templates/test_编制大纲.docx")
    
    if not os.path.exists(outline_path):
        print(f"大纲文件不存在: {outline_path}")
        return False
    
    try:
        outline = parser.parse_outline(outline_path)
        print(f"成功解析大纲，共 {len(outline)} 个章节:")
        for title, content in outline.items():
            if isinstance(content, list):
                content_preview = '\n'.join(content)[:100] + "..." if len('\n'.join(content)) > 100 else '\n'.join(content)
            else:
                content_preview = str(content)[:100] + "..." if len(str(content)) > 100 else str(content)
            print(f"  - {title}: {content_preview}")
        return True
    except Exception as e:
        print(f"大纲解析失败: {e}")
        return False

def test_chapter_outline_extraction():
    """测试章节大纲提取功能"""
    print("\n=== 测试章节大纲提取功能 ===")
    
    parser = DocumentParser()
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    outline_path = os.path.join(current_dir, "templates/test_编制大纲.docx")
    
    if not os.path.exists(outline_path):
        print(f"大纲文件不存在: {outline_path}")
        return False
    
    try:
        outline = parser.parse_outline(outline_path)
        
        # 测试不同章节的大纲提取
        test_sections = ["1 概述", "2 项目建设背景和必要性", "3 项目需求分析与预期产出"]
        
        for section_title in test_sections:
            chapter_outline = parser.get_chapter_outline(outline, section_title)
            print(f"\n章节 '{section_title}' 的大纲要求:")
            print(f"  {chapter_outline[:200]}..." if len(chapter_outline) > 200 else f"  {chapter_outline}")
        
        return True
    except Exception as e:
        print(f"章节大纲提取失败: {e}")
        return False

def test_review_guide_parsing():
    """测试审查指南解析功能"""
    print("\n=== 测试审查指南解析功能 ===")
    
    parser = DocumentParser()
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    guide_path = os.path.join(current_dir, "templates/可行性研究报告审查指南.docx")
    
    if not os.path.exists(guide_path):
        print(f"审查指南文件不存在: {guide_path}")
        return False
    
    try:
        guide_content = parser.parse_review_guide(guide_path)
        print(f"成功解析审查指南，内容长度: {len(guide_content)} 字符")
        print(f"内容预览: {guide_content[:300]}..." if len(guide_content) > 300 else f"内容: {guide_content}")
        return True
    except Exception as e:
        print(f"审查指南解析失败: {e}")
        return False

def test_system_prompt_generation():
    """测试系统提示词生成功能"""
    print("\n=== 测试系统提示词生成功能 ===")
    
    try:
        # 设置测试模式
        os.environ["TEST_MODE"] = "true"
        
        model_service = ModelService()
        
        # 模拟章节大纲和审查指南
        chapter_outline = "1. 概述\n1.1 项目概况\n1.2 编制依据\n1.3 主要技术经济指标"
        review_guide = "审查指南：请按照国家相关标准进行评审..."
        criteria_text = "审查细则1：项目概况应包括项目名称、建设地点等基本信息"
        
        # 生成系统提示词
        sys_prompt = model_service._get_system_prompt(chapter_outline, criteria_text, review_guide)
        
        print(f"成功生成系统提示词，长度: {len(sys_prompt)} 字符")
        print("提示词内容预览:")
        print(sys_prompt[:500] + "..." if len(sys_prompt) > 500 else sys_prompt)
        
        # 检查是否包含关键内容
        if "当前章节编制大纲要求" in sys_prompt and "审查指南" in sys_prompt:
            print("✓ 系统提示词包含章节大纲和审查指南")
            return True
        else:
            print("✗ 系统提示词缺少关键内容")
            return False
            
    except Exception as e:
        print(f"系统提示词生成失败: {e}")
        return False

def test_full_integration():
    """测试完整集成功能"""
    print("\n=== 测试完整集成功能 ===")
    
    try:
        # 设置测试模式
        os.environ["TEST_MODE"] = "true"
        
        model_service = ModelService()
        document_parser = DocumentParser()
        analyzer = ReportAnalyzer(model_service, document_parser)
        
        # 使用测试PDF文件
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        test_pdf = os.path.join(current_dir, "templates/test_可行性研究报告.pdf")
        
        if not os.path.exists(test_pdf):
            print(f"测试PDF文件不存在: {test_pdf}")
            return False
        
        print("开始完整分析流程...")
        result = analyzer.analyze(test_pdf)
        
        print(f"分析完成，结果包含以下字段: {list(result.keys())}")
        
        if "criteria_analysis" in result and "sections" in result:
            print(f"✓ 获得 {len(result['criteria_analysis'])} 个审查细则的分析结果")
            print(f"✓ 获得 {len(result['sections'])} 个章节的分析结果")
            return True
        else:
            print("✗ 分析结果格式不正确")
            return False
            
    except Exception as e:
        print(f"完整集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_frontend_debug_panel():
    """测试前端调试面板功能"""
    print("\n=== 测试前端调试面板功能 ===")
    
    try:
        # 检查前端文件是否包含调试面板相关代码
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        html_path = os.path.join(current_dir, "templates/index.html")
        
        if not os.path.exists(html_path):
            print(f"前端文件不存在: {html_path}")
            return False
        
        with open(html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查调试面板相关功能
        debug_features = [
            "debugPanel",
            "addDebugLog",
            "toggleDebugPanel",
            "debug-panel",
            "debug-log"
        ]
        
        missing_features = []
        for feature in debug_features:
            if feature not in html_content:
                missing_features.append(feature)
        
        if missing_features:
            print(f"✗ 缺少调试功能: {missing_features}")
            return False
        else:
            print("✓ 前端调试面板功能已正确集成")
            return True
            
    except Exception as e:
        print(f"前端调试面板测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试报告评审新优化功能...\n")
    
    tests = [
        ("大纲解析", test_outline_parsing),
        ("章节大纲提取", test_chapter_outline_extraction),
        ("审查指南解析", test_review_guide_parsing),
        ("系统提示词生成", test_system_prompt_generation),
        ("完整集成", test_full_integration),
        ("前端调试面板", test_frontend_debug_panel)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{test_name}: {'✓ 通过' if result else '✗ 失败'}")
        except Exception as e:
            print(f"\n{test_name}: ✗ 异常 - {e}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！新优化功能正常工作。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
