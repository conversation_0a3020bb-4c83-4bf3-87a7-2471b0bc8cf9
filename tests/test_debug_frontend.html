<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-panel { 
            border: 1px solid #ccc; 
            padding: 15px; 
            margin: 10px 0; 
            max-height: 400px; 
            overflow-y: auto; 
            background-color: #f8f9fa;
        }
        .debug-log { font-family: monospace; font-size: 0.9em; }
        .progress-item { padding: 5px 0; border-bottom: 1px solid #eee; }
        .timestamp { color: #666; font-size: 0.8em; }
        .status { padding: 5px 10px; margin: 5px; border-radius: 3px; }
        .status.online { background-color: #d4edda; color: #155724; }
        .status.offline { background-color: #f8d7da; color: #721c24; }
        button { margin: 5px; padding: 8px 15px; }
    </style>
</head>
<body>
    <h1>调试功能测试页面</h1>
    
    <div>
        <h3>SSE连接状态: <span id="status" class="status offline">离线</span></h3>
        <button onclick="connectSSE()">连接SSE</button>
        <button onclick="disconnectSSE()">断开SSE</button>
        <button onclick="clearLog()">清空日志</button>
        <button onclick="testAnalyze()">测试分析</button>
    </div>
    
    <div class="debug-panel">
        <h4>实时调试信息</h4>
        <div id="debugLog" class="debug-log"></div>
    </div>
    
    <script>
        let eventSource = null;
        let messageCount = 0;
        
        function updateStatus(status, isOnline) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = status;
            statusElement.className = `status ${isOnline ? 'online' : 'offline'}`;
        }
        
        function addLogMessage(message, level = 'info', timestamp = null) {
            const debugLog = document.getElementById('debugLog');
            const time = timestamp ? new Date(timestamp * 1000).toLocaleTimeString() : new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = 'progress-item';
            
            let icon = '';
            switch(level) {
                case 'success': icon = '✓'; break;
                case 'error': icon = '✗'; break;
                case 'warning': icon = '⚠'; break;
                default: icon = 'ℹ';
            }
            
            logEntry.innerHTML = `
                <span class="timestamp">[${time}]</span> 
                ${icon} ${message}
            `;
            
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
            messageCount++;
        }
        
        function connectSSE() {
            if (eventSource) {
                eventSource.close();
            }
            
            addLogMessage('正在连接SSE...', 'info');
            eventSource = new EventSource('http://localhost:8002/debug-stream');
            
            eventSource.onopen = function() {
                updateStatus('在线', true);
                addLogMessage('SSE连接已建立', 'success');
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type !== 'heartbeat') {
                        addLogMessage(data.message, data.level, data.timestamp);
                    } else {
                        // 心跳消息，更新连接状态
                        updateStatus('在线 (心跳)', true);
                    }
                } catch (e) {
                    addLogMessage(`解析消息失败: ${event.data}`, 'error');
                }
            };
            
            eventSource.onerror = function() {
                updateStatus('连接错误', false);
                addLogMessage('SSE连接发生错误', 'error');
            };
        }
        
        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                updateStatus('离线', false);
                addLogMessage('SSE连接已断开', 'warning');
            }
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
            messageCount = 0;
            addLogMessage('日志已清空', 'info');
        }
        
        async function testAnalyze() {
            addLogMessage('开始测试分析功能...', 'info');
            
            try {
                // 创建一个测试文件
                const testContent = 'test file content';
                const blob = new Blob([testContent], { type: 'application/pdf' });
                const formData = new FormData();
                formData.append('pdf_file', blob, 'test.pdf');
                
                addLogMessage('发送分析请求...', 'info');
                const response = await fetch('http://localhost:8002/analyze', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    addLogMessage('分析请求成功完成', 'success');
                } else {
                    addLogMessage(`分析请求失败: ${response.status}`, 'error');
                }
            } catch (error) {
                addLogMessage(`分析请求异常: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动连接
        window.onload = function() {
            addLogMessage('页面加载完成', 'info');
            connectSSE();
        };
        
        // 页面卸载时断开连接
        window.onbeforeunload = function() {
            if (eventSource) {
                eventSource.close();
            }
        };
    </script>
</body>
</html>
