#!/usr/bin/env python3
"""
调试 analyze_section_batch 方法
"""
import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.model_service import ModelService

# 加载环境变量
load_dotenv()

def test_analyze_section_batch():
    """测试 analyze_section_batch 方法"""
    print("开始测试 analyze_section_batch 方法...")
    
    try:
        # 初始化服务
        model_service = ModelService()
        print("✓ ModelService 初始化成功")
        
        # 准备测试数据
        project_name = "测试项目"
        section_title = "1 概述"
        section_content = """
        这是一个测试章节内容。
        项目名称：测试农村电网改造项目
        建设地点：广西某县
        建设内容：10kV线路改造
        投资规模：1000万元
        """
        
        # 简化的审查细则
        all_criteria = [
            {
                "id": "1.1",
                "content": "35千伏及以上电压等级项目以单个项目、35千伏以下电压等级项目以县域为单位编制项目可行性研究报告。"
            },
            {
                "id": "1.2", 
                "content": "项目可行性研究报告应由具备相应能力的工程咨询单位编制，并加盖工程咨询单位公章和咨询工程师(投资)执业专用章。"
            }
        ]
        
        chapter_outline = "概述项目概况、项目单位概况、编制依据、主要结论和建议等。"
        review_guide = "请确保项目名称明确具体，并包含详细的实施方案。"
        
        print(f"项目名称: {project_name}")
        print(f"章节标题: {section_title}")
        print(f"章节内容长度: {len(section_content)} 字符")
        print(f"审查细则数量: {len(all_criteria)}")
        
        # 调用被测方法
        print("\n开始调用 analyze_section_batch...")
        result = model_service.analyze_section_batch(
            project_name=project_name,
            section_title=section_title,
            section_content=section_content,
            all_criteria=all_criteria,
            chapter_outline=chapter_outline,
            review_guide=review_guide
        )
        
        print(f"\n✓ 方法调用成功")
        print(f"返回结果类型: {type(result)}")
        print(f"返回结果: {result}")
        
        # 验证返回结果
        if isinstance(result, dict) and 'criteria_results' in result:
            criteria_results = result['criteria_results']
            print(f"\n✓ 包含 criteria_results，数量: {len(criteria_results)}")
            
            for i, criterion_result in enumerate(criteria_results):
                print(f"\n审查细则 {i+1}:")
                print(f"  - criterion_id: {criterion_result.get('criterion_id', 'N/A')}")
                print(f"  - criterion_content: {criterion_result.get('criterion_content', 'N/A')[:50]}...")
                print(f"  - result: {criterion_result.get('result', 'N/A')}")
                print(f"  - explanation: {criterion_result.get('explanation', 'N/A')[:100]}...")
            
            return True
        else:
            print(f"❌ 返回结果格式不正确: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_analyze_section_batch()
    sys.exit(0 if success else 1)
