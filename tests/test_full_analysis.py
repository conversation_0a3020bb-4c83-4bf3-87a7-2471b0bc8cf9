#!/usr/bin/env python3
"""
测试完整的报告分析流程
"""
import os
from services.document_parser import DocumentParser
from services.model_service import ModelService
from services.report_analyzer import ReportAnalyzer

def test_full_analysis():
    """测试完整的报告分析流程"""
    print("开始测试完整的报告分析流程...")
    
    # 初始化服务
    document_parser = DocumentParser()
    model_service = ModelService()
    analyzer = ReportAnalyzer(model_service, document_parser)
    
    # 测试PDF路径
    pdf_path = "/mnt/d/github/ai-report2/docs/24年部分可研报告样本和评审结果/1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"PDF文件不存在: {pdf_path}")
        return False
    
    try:
        print(f"开始分析PDF: {pdf_path}")
        result = analyzer.analyze(pdf_path)
        
        print("\n=== 分析结果 ===")
        print(f"审查细则数量: {len(result['criteria_analysis'])}")
        print(f"章节数量: {len(result['sections'])}")
        
        # 显示前几个审查细则的分析结果
        print("\n=== 前3个审查细则的分析结果 ===")
        for i, criterion in enumerate(result['criteria_analysis'][:3]):
            print(f"\n审查细则 {i+1}:")
            print(f"  ID: {criterion.get('criterion_id', 'unknown')}")
            print(f"  内容: {criterion.get('criterion_content', '')[:50]}...")
            print(f"  总体结果: {criterion.get('overall_result', 'unknown')}")
            print(f"  综合分析: {criterion.get('comprehensive_analysis', '')[:100]}...")
            print(f"  整体评估: {criterion.get('overall_assessment', 'unknown')}")
            print(f"  关键发现: {len(criterion.get('key_findings', []))} 项")
            print(f"  改进建议: {len(criterion.get('recommendations', []))} 项")
        
        print(f"\n=== 总体评审意见 ===")
        summary = result['summary']
        if isinstance(summary, dict):
            print(f"总体结论: {summary.get('overall_conclusion', 'unknown')}")
            print(f"合规率: {summary.get('compliance_rate', 'unknown')}")
            print(f"主要问题数量: {len(summary.get('major_issues', []))}")
            print(f"改进建议数量: {len(summary.get('improvement_suggestions', []))}")
        else:
            print(f"总体评审意见: {summary}")
        
        print(f"\n=== 统计信息 ===")
        stats = result['statistics']
        print(f"总审查细则数: {stats['total_criteria']}")
        print(f"总章节数: {stats['total_sections']}")
        print(f"合规率: {stats['compliance_rate']}%")
        print(f"结果分布: {stats['result_distribution']}")
        
        print("\n✓ 完整分析流程测试成功！")
        return True
        
    except Exception as e:
        print(f"✗ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_full_analysis()
