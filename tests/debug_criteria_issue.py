import json
import pandas as pd
from services.document_parser import DocumentParser

print("=== 调试评审细则问题 ===")

# 1. 检查Excel文件解析
print("\n1. 检查Excel文件解析...")
try:
    parser = DocumentParser()
    criteria = parser.parse_review_criteria("templates/中央预算投资项目审核表.xlsx")
    
    print(f"解析到的审查细则数量: {len(criteria)}")
    
    # 显示所有审查细则
    for i, criterion in enumerate(criteria):
        criterion_id = criterion.get('id', 'N/A')
        content = criterion.get('content', 'N/A')
        print(f"{i+1:2d}. ID: '{criterion_id}' | 内容: {content[:50]}...")
    
    # 检查空ID
    empty_ids = [c for c in criteria if not c.get('id', '').strip()]
    if empty_ids:
        print(f"\n⚠️  发现 {len(empty_ids)} 个空ID的审查细则:")
        for i, criterion in enumerate(empty_ids):
            print(f"  {i+1}. 内容: {criterion.get('content', 'N/A')[:50]}...")
    
except Exception as e:
    print(f"Excel解析失败: {e}")
    import traceback
    traceback.print_exc()

# 2. 检查API响应数据
print("\n2. 检查API响应数据...")
try:
    with open('debug_response.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 检查原始章节数据
    sections_data = data.get('sections', [])
    print(f"原始章节数据: {len(sections_data)} 个章节")
    
    total_analysis_items = 0
    for section in sections_data:
        section_name = section.get('section', 'N/A')
        analysis = section.get('analysis', [])
        total_analysis_items += len(analysis)
        print(f"  {section_name}: {len(analysis)} 个评审项")
        
        # 检查该章节的criterion_id分布
        criterion_ids = [a.get('criterion_id', '') for a in analysis]
        unique_ids = set(criterion_ids)
        
        if len(criterion_ids) != len(unique_ids):
            print(f"    ⚠️  该章节有重复的审查细则ID!")
            from collections import Counter
            id_counts = Counter(criterion_ids)
            duplicates = {cid: count for cid, count in id_counts.items() if count > 1}
            for cid, count in duplicates.items():
                print(f"      - ID '{cid}': {count} 次")
    
    print(f"总评审项数: {total_analysis_items}")
    
    # 检查重组后的数据
    criteria_analysis = data.get('criteria_analysis', [])
    print(f"\n重组后的审查细则数据: {len(criteria_analysis)} 个审查细则")
    
    for i, criterion in enumerate(criteria_analysis):
        criterion_id = criterion.get('criterion_id', 'N/A')
        section_results = criterion.get('section_results', [])
        print(f"  {i+1:2d}. ID: '{criterion_id}' | 章节数: {len(section_results)}")
        
        # 检查是否有重复章节
        sections = [s.get('section', '') for s in section_results]
        unique_sections = set(sections)
        if len(sections) != len(unique_sections):
            print(f"      ⚠️  发现重复章节: {len(sections)} 个章节, {len(unique_sections)} 个唯一章节")
            from collections import Counter
            section_counts = Counter(sections)
            duplicates = {section: count for section, count in section_counts.items() if count > 1}
            for section, count in duplicates.items():
                print(f"        - {section}: {count} 次")

except Exception as e:
    print(f"API响应数据检查失败: {e}")

print("\n=== 问题分析 ===")
print("可能的问题原因:")
print("1. Excel文件中的审查细则数量不足21个")
print("2. 大模型批量分析时返回了重复的结果")
print("3. 数据重组算法有bug，导致重复添加章节结果")
print("4. 某些审查细则的ID为空或重复")

print("\n建议修复方案:")
print("1. 检查Excel文件，确保包含完整的21个审查细则")
print("2. 在数据重组时添加去重逻辑")
print("3. 修复大模型批量分析的提示词")
print("4. 添加数据验证和清理步骤")
