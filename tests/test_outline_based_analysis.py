#!/usr/bin/env python3
"""
测试基于大纲章节的分析优化功能
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.document_parser import DocumentParser
from services.model_service import ModelService
from services.report_analyzer import ReportAnalyzer

def test_outline_based_analysis():
    """测试基于大纲章节的分析功能"""
    print("=== 测试基于大纲章节的分析功能 ===")
    
    # 设置测试模式
    os.environ["TEST_MODE"] = "true"
    
    # 收集调试信息
    debug_messages = []
    
    def debug_callback(message: str, level: str = "info"):
        debug_messages.append({
            "message": message,
            "level": level
        })
        print(f"[{level.upper()}] {message}")
    
    try:
        model_service = ModelService()
        document_parser = DocumentParser()
        analyzer = ReportAnalyzer(model_service, document_parser)
        
        # 使用测试PDF文件
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        test_pdf = os.path.join(current_dir, "templates/test_可行性研究报告.pdf")
        
        if not os.path.exists(test_pdf):
            print(f"测试PDF文件不存在: {test_pdf}")
            return False
        
        print("开始基于大纲章节的分析...")
        result = analyzer.analyze(test_pdf, debug_callback=debug_callback)
        
        print(f"\n分析完成，结果包含以下字段: {list(result.keys())}")
        
        # 检查是否按大纲章节进行了分析
        outline_analysis_messages = [msg for msg in debug_messages if "开始分析大纲章节" in msg["message"]]
        print(f"找到 {len(outline_analysis_messages)} 个大纲章节分析记录:")
        for msg in outline_analysis_messages:
            print(f"  - {msg['message']}")
        
        # 检查章节内容提取
        content_extraction_messages = [msg for msg in debug_messages if "从PDF中提取到章节内容" in msg["message"]]
        print(f"\n找到 {len(content_extraction_messages)} 个章节内容提取记录:")
        for msg in content_extraction_messages:
            print(f"  - {msg['message']}")
        
        # 检查分析结果
        if "criteria_analysis" in result and "sections" in result:
            print(f"\n✓ 获得 {len(result['criteria_analysis'])} 个审查细则的分析结果")
            print(f"✓ 获得 {len(result['sections'])} 个章节的分析结果")
            
            # 显示章节分析结果概览
            print("\n章节分析结果概览:")
            for section in result['sections']:
                section_name = section.get('section', 'unknown')
                content_length = section.get('content_length', 0)
                has_content = section.get('has_content', False)
                analysis_count = len(section.get('analysis', []))
                print(f"  - {section_name}: 内容长度={content_length}, 有内容={has_content}, 分析项={analysis_count}")
            
            return True
        else:
            print("✗ 分析结果格式不正确")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_section_content_extraction():
    """测试章节内容提取功能"""
    print("\n=== 测试章节内容提取功能 ===")
    
    try:
        model_service = ModelService()
        document_parser = DocumentParser()
        analyzer = ReportAnalyzer(model_service, document_parser)
        
        # 加载模板
        analyzer._load_templates()
        
        # 模拟PDF章节数据
        pdf_sections = {
            "1 概述": "这是概述章节的内容...",
            "2 项目建设背景和必要性": "这是项目建设背景章节的内容...",
            "3 需求分析": "这是需求分析章节的内容...",
            "其他章节": "这是其他章节的内容..."
        }
        
        print("测试章节内容提取:")
        
        # 测试大纲中的每个章节
        for outline_title in analyzer.outline.keys():
            extracted_content = analyzer._extract_section_content_from_pdf(outline_title, pdf_sections)
            print(f"  大纲章节: {outline_title}")
            print(f"  提取内容: {extracted_content[:100]}...")
            print(f"  内容长度: {len(extracted_content)} 字符")
            print()
        
        return True
        
    except Exception as e:
        print(f"章节内容提取测试失败: {e}")
        return False

def test_outline_vs_pdf_sections():
    """对比大纲章节和PDF章节的差异"""
    print("\n=== 对比大纲章节和PDF章节 ===")
    
    try:
        model_service = ModelService()
        document_parser = DocumentParser()
        analyzer = ReportAnalyzer(model_service, document_parser)
        
        # 加载模板
        analyzer._load_templates()
        
        # 解析PDF
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        test_pdf = os.path.join(current_dir, "templates/test_可行性研究报告.pdf")
        
        if not os.path.exists(test_pdf):
            print(f"测试PDF文件不存在: {test_pdf}")
            return False
        
        project_name, pdf_sections = document_parser.parse_pdf(test_pdf)
        
        print(f"大纲章节 ({len(analyzer.outline)} 个):")
        for i, outline_title in enumerate(analyzer.outline.keys(), 1):
            print(f"  {i}. {outline_title}")
        
        print(f"\nPDF提取章节 ({len(pdf_sections)} 个):")
        for i, pdf_title in enumerate(pdf_sections.keys(), 1):
            print(f"  {i}. {pdf_title}")
        
        print("\n章节匹配情况:")
        for outline_title in analyzer.outline.keys():
            if outline_title in pdf_sections:
                print(f"  ✓ {outline_title} - 直接匹配")
            else:
                # 尝试模糊匹配
                extracted_content = analyzer._extract_section_content_from_pdf(outline_title, pdf_sections)
                if "未找到" in extracted_content:
                    print(f"  ✗ {outline_title} - 未找到匹配")
                else:
                    print(f"  ~ {outline_title} - 模糊匹配")
        
        return True
        
    except Exception as e:
        print(f"对比测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试基于大纲章节的分析优化功能...\n")
    
    tests = [
        ("章节内容提取", test_section_content_extraction),
        ("大纲vs PDF章节对比", test_outline_vs_pdf_sections),
        ("基于大纲的完整分析", test_outline_based_analysis)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{test_name}: {'✓ 通过' if result else '✗ 失败'}")
        except Exception as e:
            print(f"\n{test_name}: ✗ 异常 - {e}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！基于大纲章节的分析优化功能正常工作。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
