<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传功能调试测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { 
            border: 1px solid #ccc; 
            padding: 15px; 
            margin: 10px 0; 
            max-height: 400px; 
            overflow-y: auto; 
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 0.9em;
        }
        .log-entry { padding: 5px 0; border-bottom: 1px solid #eee; }
        .timestamp { color: #666; font-size: 0.8em; }
        button { margin: 5px; padding: 8px 15px; }
        .status { padding: 5px 10px; margin: 5px; border-radius: 3px; }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>上传功能调试测试</h1>
    
    <div>
        <h3>测试步骤</h3>
        <button onclick="testServerConnection()">1. 测试服务器连接</button>
        <button onclick="testSSEConnection()">2. 测试SSE连接</button>
        <button onclick="testFileUpload()">3. 测试文件上传</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div>
        <h3>文件上传测试</h3>
        <form id="testForm">
            <input type="file" id="fileInput" accept=".pdf">
            <button type="submit">提交测试</button>
        </form>
    </div>
    
    <div class="log" id="logContainer"></div>
    
    <script>
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            let icon = '';
            switch(type) {
                case 'success': icon = '✓'; break;
                case 'error': icon = '✗'; break;
                case 'warning': icon = '⚠'; break;
                default: icon = 'ℹ';
            }
            
            logEntry.innerHTML = `
                <span class="timestamp">[${timestamp}]</span> 
                <span class="status ${type}">${icon}</span> ${message}
            `;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }
        
        async function testServerConnection() {
            addLog('开始测试服务器连接...', 'info');
            
            try {
                const response = await fetch('http://localhost:8003/', {
                    method: 'GET'
                });
                
                if (response.ok) {
                    addLog(`服务器连接成功: ${response.status}`, 'success');
                    addLog(`响应头: ${JSON.stringify(Object.fromEntries(response.headers))}`, 'info');
                } else {
                    addLog(`服务器连接失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addLog(`服务器连接异常: ${error.message}`, 'error');
            }
        }
        
        function testSSEConnection() {
            addLog('开始测试SSE连接...', 'info');
            
            const eventSource = new EventSource('http://localhost:8003/debug-stream');
            let messageCount = 0;
            
            eventSource.onopen = function() {
                addLog('SSE连接已建立', 'success');
            };
            
            eventSource.onmessage = function(event) {
                messageCount++;
                try {
                    const data = JSON.parse(event.data);
                    addLog(`收到SSE消息 #${messageCount}: ${JSON.stringify(data)}`, 'info');
                } catch (e) {
                    addLog(`收到SSE原始消息 #${messageCount}: ${event.data}`, 'info');
                }
                
                // 收到3条消息后关闭连接
                if (messageCount >= 3) {
                    eventSource.close();
                    addLog('SSE连接已关闭（测试完成）', 'success');
                }
            };
            
            eventSource.onerror = function() {
                addLog('SSE连接发生错误', 'error');
                eventSource.close();
            };
            
            // 10秒后自动关闭
            setTimeout(() => {
                if (eventSource.readyState !== EventSource.CLOSED) {
                    eventSource.close();
                    addLog('SSE连接超时关闭', 'warning');
                }
            }, 10000);
        }
        
        async function testFileUpload() {
            addLog('开始测试文件上传...', 'info');
            
            // 创建一个测试文件
            const testContent = 'This is a test PDF file content for debugging upload functionality.';
            const blob = new Blob([testContent], { type: 'application/pdf' });
            const formData = new FormData();
            formData.append('pdf_file', blob, 'test_debug.pdf');
            
            addLog(`创建测试文件: test_debug.pdf (${blob.size} bytes)`, 'info');
            
            try {
                addLog('发送上传请求...', 'info');
                const response = await fetch('http://localhost:8003/analyze', {
                    method: 'POST',
                    body: formData
                });
                
                addLog(`上传响应状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const result = await response.json();
                    addLog(`上传成功，响应数据: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    addLog(`上传失败，错误信息: ${errorText}`, 'error');
                }
            } catch (error) {
                addLog(`上传异常: ${error.message}`, 'error');
                addLog(`错误详情: ${error.stack}`, 'error');
            }
        }
        
        // 表单提交测试
        document.getElementById('testForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                addLog('请先选择文件', 'warning');
                return;
            }
            
            addLog(`开始上传文件: ${file.name} (${file.size} bytes, ${file.type})`, 'info');
            
            const formData = new FormData();
            formData.append('pdf_file', file);
            
            try {
                addLog('发送表单上传请求...', 'info');
                const response = await fetch('http://localhost:8003/analyze', {
                    method: 'POST',
                    body: formData
                });
                
                addLog(`表单上传响应: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const result = await response.json();
                    addLog(`表单上传成功: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    addLog(`表单上传失败: ${errorText}`, 'error');
                }
            } catch (error) {
                addLog(`表单上传异常: ${error.message}`, 'error');
            }
        });
        
        // 页面加载时自动测试服务器连接
        window.onload = function() {
            addLog('页面加载完成，开始自动测试...', 'info');
            testServerConnection();
        };
    </script>
</body>
</html>
