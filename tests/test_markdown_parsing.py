#!/usr/bin/env python3
"""
测试markdown解析功能
验证新的解析规则和格式检查
"""

import sys
import os
import re

def test_markdown_parsing():
    """测试markdown解析功能"""
    print("=" * 60)
    print("🧪 测试markdown解析功能")
    print("=" * 60)
    
    # 测试文件路径
    md_file = "templates/1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告.md"
    
    if not os.path.exists(md_file):
        print(f"❌ 测试文件不存在: {md_file}")
        return False
    
    try:
        # 1. 读取markdown文件
        print("1. 读取markdown文件...")
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✓ 文件读取成功，内容长度: {len(content)} 字符")
        
        # 2. 分析markdown结构
        print("\n2. 分析markdown结构...")
        lines = content.split('\n')
        
        # 统计标题
        h1_titles = []
        h2_titles = []
        
        for line in lines:
            line_stripped = line.strip()
            if line_stripped.startswith('# ') and not line_stripped.startswith('## '):
                h1_titles.append(line_stripped[2:].strip())
            elif line_stripped.startswith('## '):
                h2_titles.append(line_stripped[3:].strip())
        
        print(f"  一级标题(#)数量: {len(h1_titles)}")
        print(f"  二级标题(##)数量: {len(h2_titles)}")
        
        # 3. 显示一级标题
        print("\n3. 一级标题列表:")
        for i, title in enumerate(h1_titles):
            print(f"  {i+1}. {title}")
        
        # 4. 检查概述章节位置
        print("\n4. 检查概述章节...")
        overview_found = False
        overview_index = -1
        
        for i, title in enumerate(h1_titles):
            if is_overview_chapter(title):
                overview_found = True
                overview_index = i
                print(f"  ✓ 找到概述章节: '{title}' (第{i+1}个一级标题)")
                break
        
        if not overview_found:
            print("  ❌ 未找到概述章节")
        
        # 5. 检查资质证书内容
        print("\n5. 检查资质证书内容...")
        if overview_found and overview_index > 0:
            print(f"  ✓ 概述之前有 {overview_index} 个章节，可归类为资质证书")
            
            # 显示概述之前的章节
            print("  概述之前的章节:")
            for i in range(overview_index):
                print(f"    {i+1}. {h1_titles[i]}")
        else:
            print("  ⚠️  概述是第一个章节，没有资质证书内容")
        
        # 6. 检查二级标题格式
        print("\n6. 检查二级标题格式...")
        valid_h2_count = 0
        invalid_h2_titles = []
        
        for title in h2_titles:
            if validate_subsection_format(title):
                valid_h2_count += 1
            else:
                invalid_h2_titles.append(title)
        
        print(f"  符合格式的二级标题: {valid_h2_count}/{len(h2_titles)}")
        
        if invalid_h2_titles:
            print("  格式不符合要求的二级标题:")
            for title in invalid_h2_titles[:10]:  # 只显示前10个
                print(f"    ❌ {title}")
            if len(invalid_h2_titles) > 10:
                print(f"    ... 还有 {len(invalid_h2_titles) - 10} 个")
        
        # 7. 模拟解析过程
        print("\n7. 模拟解析过程...")
        sections = simulate_parsing(content)
        
        print(f"  解析结果: {len(sections)} 个章节")
        for section_name, content_length in sections.items():
            status = "✓ 有内容" if content_length > 0 else "✗ 无内容"
            print(f"    {section_name}: {status} ({content_length}字符)")
        
        # 8. 验证特定要求
        print("\n8. 验证特定要求:")
        
        # 检查资质证书章节
        if "资质证书" in sections:
            print("  ✓ 资质证书章节存在")
        else:
            print("  ❌ 资质证书章节缺失")
        
        # 检查概述章节
        overview_sections = [name for name in sections.keys() if "概述" in name]
        if overview_sections:
            print(f"  ✓ 概述章节存在: {overview_sections[0]}")
        else:
            print("  ❌ 概述章节缺失")
        
        # 检查章节顺序
        section_names = list(sections.keys())
        if "资质证书" in section_names and overview_sections:
            cert_index = section_names.index("资质证书")
            overview_index = section_names.index(overview_sections[0])
            if cert_index < overview_index:
                print("  ✓ 资质证书在概述之前")
            else:
                print("  ❌ 资质证书不在概述之前")
        
        print("\n" + "=" * 60)
        print("✅ markdown解析测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def is_overview_chapter(title: str) -> bool:
    """判断是否是概述章节"""
    overview_keywords = ['概述', '项目概况', '总体情况', '基本情况', '项目基本情况']
    
    for keyword in overview_keywords:
        if keyword in title:
            return True
    
    # 检查是否包含数字1
    if '1' in title and any(keyword in title for keyword in ['概述', '概况']):
        return True
        
    return False

def validate_subsection_format(subsection_title: str) -> bool:
    """验证二级标题格式是否符合要求（## +章节序号）"""
    
    # 检查是否以数字开头，如：1.1 项目概况
    pattern = r'^\d+\.\d+\s+.+'
    if re.match(pattern, subsection_title):
        return True
    
    # 检查其他可能的格式
    patterns = [
        r'^\d+\.\d+\.\d+\s+.+',  # 1.1.1 格式
        r'^\(\d+\)\s+.+',        # (1) 格式
        r'^（\d+）\s+.+',        # （1）格式
    ]
    
    for pattern in patterns:
        if re.match(pattern, subsection_title):
            return True
    
    return False

def simulate_parsing(content: str) -> dict:
    """模拟解析过程，返回章节和内容长度"""
    sections = {}
    lines = content.split('\n')
    current_section = None
    current_content = []
    found_overview = False
    
    for line in lines:
        line_stripped = line.strip()
        
        # 检查是否是一级章节标题（一个#）
        if line_stripped.startswith('# ') and not line_stripped.startswith('## '):
            chapter_title = line_stripped[2:].strip()
            
            # 保存当前章节内容
            if current_section:
                sections[current_section] = len('\n'.join(current_content).strip())
            
            # 检查是否是概述章节
            if is_overview_chapter(chapter_title):
                found_overview = True
                current_section = f"1 概述"  # 标准化章节名
            elif not found_overview:
                # 如果还没找到概述，归类到资质证书
                current_section = "资质证书"
            else:
                # 已找到概述后的其他章节，尝试匹配标准章节
                current_section = match_standard_chapter(chapter_title)
            
            current_content = []
        else:
            # 添加内容到当前章节
            if current_section:
                current_content.append(line)
            elif not found_overview:
                # 如果还没有找到概述，且没有当前章节，归类到资质证书
                current_section = "资质证书"
                current_content = [line]
    
    # 保存最后一个章节的内容
    if current_section:
        sections[current_section] = len('\n'.join(current_content).strip())
    
    return sections

def match_standard_chapter(title: str) -> str:
    """将章节标题匹配到标准章节"""
    standard_outline = {
        "1": "概述",
        "2": "项目建设背景和必要性",
        "3": "项目需求分析与预期产出",
        "4": "项目选址与要素保障",
        "5": "项目建设方案",
        "6": "项目运营方案",
        "7": "项目投融资与财务方案",
        "8": "项目影响效果分析",
        "9": "项目风险管控方案",
        "10": "研究结论及建议",
        "11": "附表"
    }
    
    # 首先尝试精确匹配
    for num, standard_title in standard_outline.items():
        full_title = f"{num} {standard_title}"
        if title == standard_title or title == full_title:
            return full_title
    
    # 然后尝试关键词匹配
    for num, standard_title in standard_outline.items():
        # 简单的关键词匹配
        if any(keyword in title for keyword in standard_title.split()):
            return f"{num} {standard_title}"
    
    # 如果没有匹配到，返回原标题
    return title

if __name__ == "__main__":
    success = test_markdown_parsing()
    sys.exit(0 if success else 1)
