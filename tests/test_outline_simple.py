#!/usr/bin/env python3
"""
简化的大纲解析测试脚本
用于验证优化后的解析逻辑
"""
import re
from typing import Dict, List, Any, Tuple

class SimpleDocumentParser:
    def __init__(self):
        self.standard_outline = {
            "1": "概述",
            "2": "项目建设背景和必要性",
            "3": "项目需求分析与预期产出",
            "4": "项目选址与要素保障",
            "5": "项目建设方案",
            "6": "项目运营方案",
            "7": "项目投融资与财务方案",
            "8": "项目影响效果分析",
            "9": "项目风险管控方案",
            "10": "研究结论及建议",
            "11": "附表"
        }

    def _is_main_chapter_title(self, text: str) -> tuple[bool, str]:
        """判断是否为主要章节标题（1-11章），不包括子标题"""
        text = text.strip()
        
        # 匹配主要章节编号的模式（只匹配1-11的单数字）
        main_chapter_patterns = [
            r'^(\d{1,2})[.、\s]*(.+)$',  # 1概述, 1. 概述, 1 概述, 10 研究结论及建议
            r'^第(\d{1,2})章[.、\s]*(.+)$',  # 第1章 概述
        ]
        
        for pattern in main_chapter_patterns:
            match = re.match(pattern, text)
            if match:
                chapter_num = match.group(1)
                chapter_title = match.group(2).strip().rstrip('.')
                
                # 只处理1-11章的主要章节
                if chapter_num in self.standard_outline:
                    standard_title = self.standard_outline[chapter_num]
                    if (chapter_title == standard_title or
                        standard_title in chapter_title or
                        chapter_title in standard_title):
                        return True, f"{chapter_num} {standard_title}"
        
        # 匹配纯章节标题（不带编号）
        for num, title in self.standard_outline.items():
            if text == title or (title in text and len(text) <= len(title) + 10):
                return True, f"{num} {title}"
        
        return False, ""

    def _is_sub_title(self, text: str) -> bool:
        """判断是否为子标题（如1.1、1.2等），需要过滤掉"""
        text = text.strip()
        
        # 子标题模式
        sub_title_patterns = [
            r'^\d+\.\d+',  # 1.1, 1.2, 2.1 等
            r'^\(\d+\)',   # (1), (2) 等
            r'^（\d+）',   # （1）, （2） 等
            r'^\d+）',     # 1）, 2） 等
            r'^[a-zA-Z]\.',  # a., b., A., B. 等
        ]
        
        for pattern in sub_title_patterns:
            if re.match(pattern, text):
                return True
        
        # 如果文本很短且只包含数字和少量文字，可能是子标题
        if len(text) < 20 and re.match(r'^[\d\.\s\u4e00-\u9fa5]{1,15}$', text):
            # 检查是否包含常见的子标题关键词
            sub_keywords = ['概况', '简述', '说明', '分析', '计算', '确定', '选择']
            if any(keyword in text for keyword in sub_keywords):
                return True
        
        return False

    def _validate_outline_content(self, outline: Dict[str, Any]) -> None:
        """验证大纲内容，确保每个章节都有足够的内容"""
        print(f"\n{'='*40} 验证大纲内容 {'='*40}")
        
        for chapter_title, content in outline.items():
            if isinstance(content, list):
                total_chars = sum(len(item) for item in content)
                print(f"章节 '{chapter_title}': {len(content)} 个段落, {total_chars} 字符")
                
                if total_chars < 30:
                    print(f"⚠️  章节 '{chapter_title}' 内容不足（少于30字符）")
                else:
                    print(f"✓ 章节 '{chapter_title}' 内容充足")
            else:
                content_length = len(str(content)) if content else 0
                print(f"章节 '{chapter_title}': {content_length} 字符")
                
                if content_length < 30:
                    print(f"⚠️  章节 '{chapter_title}' 内容不足（少于30字符）")
                else:
                    print(f"✓ 章节 '{chapter_title}' 内容充足")

def test_chapter_recognition():
    """测试章节识别功能"""
    parser = SimpleDocumentParser()
    
    # 测试用例
    test_cases = [
        "1 概述",
        "1. 概述", 
        "1.1 项目概况",  # 应该被识别为子标题
        "2 项目建设背景和必要性",
        "2.1 项目建设背景",  # 应该被识别为子标题
        "概述",
        "项目建设背景和必要性",
        "7.2 盈利能力分析",  # 应该被识别为子标题
        "10 研究结论及建议",
        "11 附表",
        "（1）基本情况",  # 应该被识别为子标题
        "投资估算编制说明应包括估算编制依据和编制范围",  # 普通内容
    ]
    
    print("测试章节标题识别:")
    for test_text in test_cases:
        is_main, main_title = parser._is_main_chapter_title(test_text)
        is_sub = parser._is_sub_title(test_text)
        
        print(f"'{test_text}' -> 主章节: {is_main} ({main_title}), 子标题: {is_sub}")

def test_content_validation():
    """测试内容验证功能"""
    parser = SimpleDocumentParser()
    
    # 模拟解析结果
    test_outline = {
        "1 概述": [
            "1.1 项目概况",
            "概述项目名称、建设目标和任务、建设地点、建设内容和规模、建设工期、投资规模和资金来源、建设模式、主要技术经济指标、绩效目标等。",
            "1.2项目单位概况",
            "简述项目单位基本情况。拟新组建项目法人的，简述项目法人组建方案。"
        ],
        "2 项目建设背景和必要性": [
            "项目所在县域的经济社会、自然环境等简况，电力系统简况。"
        ],
        "3 项目需求分析与预期产出": [],  # 空章节
        "11 附表": ["附表内容很少"]  # 内容不足的章节
    }
    
    parser._validate_outline_content(test_outline)

if __name__ == "__main__":
    print("开始简化测试...")
    test_chapter_recognition()
    print("\n" + "="*60)
    test_content_validation()
    print("\n测试完成")
