#!/usr/bin/env python3
"""
测试计时统计功能
"""
import requests
import json
import os

def test_timing_stats():
    """测试计时统计功能"""
    print("=== 测试计时统计功能 ===")

    # 设置测试模式
    os.environ["TEST_MODE"] = "true"

    try:
        # 重新导入服务以应用测试模式
        from services.report_analyzer import ReportAnalyzer
        from services.document_parser import DocumentParser
        from services.model_service import ModelService
        print("✓ 服务导入成功")
    except Exception as e:
        print(f"✗ 服务导入失败: {e}")
        return

    # 初始化服务
    model_service = ModelService()
    document_parser = DocumentParser()
    report_analyzer = ReportAnalyzer(model_service, document_parser)

    print("1. 测试模型服务统计功能...")

    # 重置统计信息
    model_service.reset_stats()
    initial_stats = model_service.get_stats()
    print(f"初始统计信息: {initial_stats}")

    # 模拟一些API调用（在测试模式下）
    print("\n2. 模拟API调用...")

    # 测试章节分析
    result = model_service.analyze_section_batch(
        project_name="测试项目",
        section_title="测试章节",
        section_content="测试内容",
        all_criteria=[{"id": "test_1", "content": "测试审查细则"}],
        chapter_outline="测试大纲",
        review_guide="测试指南"
    )

    print(f"章节分析结果: {result}")

    # 获取更新后的统计信息
    updated_stats = model_service.get_stats()
    print(f"\n更新后统计信息: {updated_stats}")

    print("\n3. 测试完整的报告分析流程...")

    # 在测试模式下，我们需要模拟PDF解析，所以直接测试分析器的其他功能
    try:
        # 测试统计信息生成
        mock_review_results = [
            {
                "section": "测试章节1",
                "analysis": [
                    {"criterion_id": "1.1", "result": "符合", "explanation": "测试说明1"},
                    {"criterion_id": "1.2", "result": "不符合", "explanation": "测试说明2"}
                ]
            }
        ]

        stats = report_analyzer._generate_statistics(mock_review_results)
        print(f"统计信息生成测试: {stats}")

        # 测试模型服务的汇总功能
        summary_result = model_service.summarize_review(mock_review_results)
        print(f"汇总功能测试: {summary_result}")

        # 获取最终的统计信息
        final_stats = model_service.get_stats()
        print(f"\n最终统计信息: {final_stats}")

        # 模拟一个完整的分析结果
        analysis_result = {
            "criteria_analysis": [],
            "sections": mock_review_results,
            "summary": summary_result["summary"],
            "statistics": stats,
            "timing_stats": {
                "execution_time": 2.3,
                "total_input_tokens": final_stats["total_input_tokens"],
                "total_output_tokens": final_stats["total_output_tokens"],
                "total_tokens": final_stats["total_tokens"],
                "api_calls": final_stats["api_calls"],
                "total_api_time": final_stats["total_api_time"]
            }
        }

        print(f"\n分析结果包含的键: {list(analysis_result.keys())}")

        # 检查是否包含计时统计信息
        if "timing_stats" in analysis_result:
            timing_stats = analysis_result["timing_stats"]
            print(f"\n计时统计信息:")
            print(f"  执行时间: {timing_stats.get('execution_time', 0)}秒")
            print(f"  输入Token: {timing_stats.get('total_input_tokens', 0)}")
            print(f"  输出Token: {timing_stats.get('total_output_tokens', 0)}")
            print(f"  总Token: {timing_stats.get('total_tokens', 0)}")
            print(f"  API调用次数: {timing_stats.get('api_calls', 0)}")
            print(f"  API总耗时: {timing_stats.get('total_api_time', 0)}秒")

            print("\n✓ 计时统计功能正常工作！")
        else:
            print("\n✗ 缺少计时统计信息")

    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

    # 恢复原始设置
    os.environ["TEST_MODE"] = "false"

    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_timing_stats()
