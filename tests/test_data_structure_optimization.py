#!/usr/bin/env python3
"""
测试数据结构优化功能
验证按照审查细则组织格式重新组织数据结构的功能
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.report_analyzer import ReportAnalyzer
from services.document_parser import DocumentParser
from services.model_service import ModelService

def test_data_structure_optimization():
    """测试数据结构优化功能"""
    print("=" * 60)
    print("🧪 测试数据结构优化功能")
    print("=" * 60)

    try:
        # 1. 初始化服务（跳过ModelService以避免API密钥问题）
        print("1. 初始化服务...")
        document_parser = DocumentParser()
        # 创建一个模拟的ReportAnalyzer实例，只测试数据结构方法
        class MockReportAnalyzer:
            def __init__(self):
                pass

            def _generate_section_summary(self, section):
                """生成章节内容摘要"""
                if not section.get("has_content", False):
                    return "该章节在可研报告中无相关内容"

                # 统计该章节的评审结果
                analysis = section.get("analysis", [])
                if not analysis:
                    return "该章节未进行评审分析"

                result_counts = {"符合": 0, "基本符合": 0, "不符合": 0, "不适用": 0}
                for item in analysis:
                    result = item.get("result", "不适用")
                    if result in result_counts:
                        result_counts[result] += 1
                    else:
                        result_counts["不适用"] += 1

                # 生成摘要文本
                total_applicable = result_counts["符合"] + result_counts["基本符合"] + result_counts["不符合"]
                if total_applicable == 0:
                    return "该章节的所有审查细则均不适用"

                compliant = result_counts["符合"] + result_counts["基本符合"]
                summary = f"该章节共涉及{total_applicable}个审查细则，其中{compliant}个符合要求"

                if result_counts["不符合"] > 0:
                    summary += f"，{result_counts['不符合']}个不符合要求"

                return summary + "。"

            def _organize_review_results_by_criteria(self, enhanced_criteria):
                """按照参考格式重新组织审查细则结果"""
                organized_results = []

                for criterion in enhanced_criteria:
                    # 构建章节评审结果列表
                    sections_results = []
                    for section_result in criterion.get("section_results", []):
                        section_info = {
                            "section_name": section_result.get("section", ""),
                            "result": section_result.get("result", "不适用"),
                            "explanation": section_result.get("explanation", "")
                        }
                        sections_results.append(section_info)

                    # 构建按照参考格式的审查细则结果
                    organized_criterion = {
                        "criterion_id": criterion.get("criterion_id", ""),
                        "criterion_content": criterion.get("criterion_content", ""),
                        "sections": sections_results
                    }

                    organized_results.append(organized_criterion)

                return organized_results

        report_analyzer = MockReportAnalyzer()
        print("✓ 服务初始化完成（使用模拟实例）")

        # 2. 创建模拟数据
        print("\n2. 创建模拟数据...")

        # 模拟章节评审结果
        mock_review_results = [
            {
                "section": "1 概述",
                "content_length": 1613,
                "has_content": True,
                "analysis": [
                    {
                        "criterion_id": "1.1",
                        "criterion_content": "35千伏及以上电压等级项目以单个项目、35千伏以下电压等级项目以县域为单位编制项目可行性研究报告。",
                        "result": "基本符合",
                        "explanation": "项目为10kV及以下电压等级，建设地点位于横州市，但章节内容未明确说明以县域为单位编制，需补充说明。"
                    },
                    {
                        "criterion_id": "1.2",
                        "criterion_content": "项目可行性研究报告应由具备相应能力的工程咨询单位编制。",
                        "result": "不符合",
                        "explanation": "章节内容未提及编制单位信息。"
                    }
                ]
            },
            {
                "section": "2 项目建设背景和必要性",
                "content_length": 2613,
                "has_content": True,
                "analysis": [
                    {
                        "criterion_id": "1.1",
                        "criterion_content": "35千伏及以上电压等级项目以单个项目、35千伏以下电压等级项目以县域为单位编制项目可行性研究报告。",
                        "result": "符合",
                        "explanation": "符合"
                    },
                    {
                        "criterion_id": "1.2",
                        "criterion_content": "项目可行性研究报告应由具备相应能力的工程咨询单位编制。",
                        "result": "符合",
                        "explanation": "符合"
                    }
                ]
            }
        ]

        # 模拟增强的审查细则数据
        mock_enhanced_criteria = [
            {
                "criterion_id": "1.1",
                "criterion_content": "35千伏及以上电压等级项目以单个项目、35千伏以下电压等级项目以县域为单位编制项目可行性研究报告。",
                "section_results": [
                    {
                        "section": "1 概述",
                        "has_content": True,
                        "result": "基本符合",
                        "explanation": "项目为10kV及以下电压等级，建设地点位于横州市，但章节内容未明确说明以县域为单位编制，需补充说明。"
                    },
                    {
                        "section": "2 项目建设背景和必要性",
                        "has_content": True,
                        "result": "符合",
                        "explanation": "符合"
                    }
                ],
                "overall_result": "基本符合",
                "comprehensive_analysis": "项目整体符合县域编制要求，但需要在概述章节中明确说明编制单位范围。",
                "overall_assessment": "基本符合",
                "key_findings": ["项目为10kV电压等级", "建设地点位于横州市"],
                "recommendations": ["建议在概述章节中明确说明以县域为单位编制"]
            },
            {
                "criterion_id": "1.2",
                "criterion_content": "项目可行性研究报告应由具备相应能力的工程咨询单位编制。",
                "section_results": [
                    {
                        "section": "1 概述",
                        "has_content": True,
                        "result": "不符合",
                        "explanation": "章节内容未提及编制单位信息。"
                    },
                    {
                        "section": "2 项目建设背景和必要性",
                        "has_content": True,
                        "result": "符合",
                        "explanation": "符合"
                    }
                ],
                "overall_result": "基本符合",
                "comprehensive_analysis": "项目编制单位信息不完整，需要补充相关资质证明。",
                "overall_assessment": "基本符合",
                "key_findings": ["编制单位信息缺失", "部分章节符合要求"],
                "recommendations": ["补充编制单位资质信息", "加盖相关印章"]
            }
        ]

        print("✓ 模拟数据创建完成")

        # 3. 测试章节摘要生成
        print("\n3. 测试章节摘要生成...")
        for section in mock_review_results:
            summary = report_analyzer._generate_section_summary(section)
            print(f"✓ 章节 '{section['section']}' 摘要: {summary}")

        # 4. 测试审查细则结果重组
        print("\n4. 测试审查细则结果重组...")
        organized_results = report_analyzer._organize_review_results_by_criteria(mock_enhanced_criteria)

        print(f"✓ 重组完成，共 {len(organized_results)} 个审查细则")

        # 5. 验证重组后的数据结构
        print("\n5. 验证重组后的数据结构...")
        for i, criterion in enumerate(organized_results):
            print(f"  审查细则 {i+1}:")
            print(f"    - criterion_id: {criterion.get('criterion_id', 'N/A')}")
            print(f"    - criterion_content: {criterion.get('criterion_content', 'N/A')[:50]}...")
            print(f"    - sections 数量: {len(criterion.get('sections', []))}")

            # 验证sections字段结构
            sections = criterion.get('sections', [])
            for j, section in enumerate(sections):
                required_fields = ['section_name', 'result', 'explanation']
                missing_fields = [field for field in required_fields if field not in section]
                if missing_fields:
                    print(f"      ❌ 章节 {j+1} 缺少字段: {missing_fields}")
                else:
                    print(f"      ✓ 章节 {j+1}: {section['section_name']} - {section['result']}")

        # 6. 生成完整的测试数据结构
        print("\n6. 生成完整的测试数据结构...")

        # 构建章节信息
        sections_info = []
        for section in mock_review_results:
            section_info = {
                "section_name": section["section"],
                "content_length": section["content_length"],
                "has_content": section["has_content"],
                "summary": report_analyzer._generate_section_summary(section)
            }
            sections_info.append(section_info)

        # 构建完整的返回数据结构
        complete_result = {
            "sections": sections_info,
            "review_results": organized_results,
            "summary": "这是一个测试总结",
            "statistics": {
                "total_criteria": len(organized_results),
                "total_sections": len(sections_info),
                "result_distribution": {"符合": 2, "基本符合": 2, "不符合": 0, "不适用": 0},
                "compliance_rate": 100.0
            },
            "timing_stats": {
                "execution_time": 10.5,
                "total_input_tokens": 1000,
                "total_output_tokens": 500,
                "total_tokens": 1500,
                "api_calls": 3,
                "total_api_time": 8.2
            },
            # 保留原有字段以确保向后兼容
            "criteria_analysis": mock_enhanced_criteria,
            "sections_detail": mock_review_results
        }

        # 7. 保存测试结果
        output_file = "test_data_structure_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(complete_result, f, ensure_ascii=False, indent=2)

        print(f"✓ 完整测试结果已保存到: {output_file}")

        # 8. 验证与参考格式的一致性
        print("\n7. 验证与参考格式的一致性...")

        # 检查必需字段
        required_top_level_fields = ['sections', 'review_results']
        for field in required_top_level_fields:
            if field in complete_result:
                print(f"  ✓ 顶级字段 '{field}': 存在")
            else:
                print(f"  ❌ 顶级字段 '{field}': 缺失")

        # 检查sections字段结构
        if 'sections' in complete_result:
            sections = complete_result['sections']
            required_section_fields = ['section_name', 'content_length', 'has_content', 'summary']
            for i, section in enumerate(sections):
                missing_fields = [field for field in required_section_fields if field not in section]
                if missing_fields:
                    print(f"  ❌ sections[{i}] 缺少字段: {missing_fields}")
                else:
                    print(f"  ✓ sections[{i}]: 结构完整")

        # 检查review_results字段结构
        if 'review_results' in complete_result:
            review_results = complete_result['review_results']
            required_criterion_fields = ['criterion_id', 'criterion_content', 'sections']
            for i, criterion in enumerate(review_results):
                missing_fields = [field for field in required_criterion_fields if field not in criterion]
                if missing_fields:
                    print(f"  ❌ review_results[{i}] 缺少字段: {missing_fields}")
                else:
                    print(f"  ✓ review_results[{i}]: 结构完整")

                # 检查sections子字段
                if 'sections' in criterion:
                    criterion_sections = criterion['sections']
                    required_section_fields = ['section_name', 'result', 'explanation']
                    for j, section in enumerate(criterion_sections):
                        missing_fields = [field for field in required_section_fields if field not in section]
                        if missing_fields:
                            print(f"    ❌ review_results[{i}].sections[{j}] 缺少字段: {missing_fields}")
                        else:
                            print(f"    ✓ review_results[{i}].sections[{j}]: 结构完整")

        print("\n" + "=" * 60)
        print("✅ 数据结构优化测试完成！")
        print("=" * 60)

        return True

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_data_structure_optimization()
    sys.exit(0 if success else 1)
