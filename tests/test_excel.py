import pandas as pd
import os

# 测试 Excel 文件解析
excel_path = "agent/templates/中央预算投资项目审核表.xlsx"

try:
    df = pd.read_excel(excel_path)
    print("Excel 文件读取成功")
    print(f"行数: {len(df)}")
    print(f"列名: {list(df.columns)}")
    #print("\n前5行数据:")
    #print(df.head())
    
    # 输出各行的数据：序号、审查范畴、审查细则、审查情况
    print("------------------------------------------------------------")
    print("序号\t审查范畴\t\t审查细则\t\t审查情况")
    print("------------------------------------------------------------")
    for i in range(len(df)):
        print(f"{i+1}\t{df.iat[i, 0]}\t\t{df.iat[i, 1]}\t\t{df.iat[i, 2]}")
            
except Exception as e:
    print(f"解析 Excel 文件失败: {e}")
