#!/usr/bin/env python3
import os
import sys
from services.document_parser import DocumentParser
from services.model_service import ModelService
from services.report_analyzer import ReportAnalyzer

def test_components():
    print("测试各个组件...")

    # 测试文档解析器
    print("1. 测试文档解析器...")
    try:
        document_parser = DocumentParser()
        print("✓ 文档解析器初始化成功")

        # 测试解析大纲
        current_dir = os.path.dirname(os.path.abspath(__file__))
        outline_path = os.path.join(current_dir, "templates", "可行性研究报告编制和审查指南.docx")
        print(f"大纲文件路径: {outline_path}")
        print(f"文件是否存在: {os.path.exists(outline_path)}")

        if os.path.exists(outline_path):
            outline = document_parser.parse_outline(outline_path)
            print(f"✓ 大纲解析成功，包含 {len(outline)} 个章节")
        else:
            print("✗ 大纲文件不存在")
            return False

        # 测试解析审查细则
        criteria_path = os.path.join(current_dir, "templates", "中央预算投资项目审核表.xlsx")
        print(f"审查细则文件路径: {criteria_path}")
        print(f"文件是否存在: {os.path.exists(criteria_path)}")

        if os.path.exists(criteria_path):
            criteria = document_parser.parse_review_criteria(criteria_path)
            print(f"✓ 审查细则解析成功，包含 {len(criteria)} 个细则")
        else:
            print("✗ 审查细则文件不存在")
            return False

    except Exception as e:
        print(f"✗ 文档解析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    # 测试模型服务
    print("\n2. 测试模型服务...")
    try:
        model_service = ModelService()
        print("✓ 模型服务初始化成功")

        # 简单测试
        test_result = model_service.analyze_section_batch(
            "测试章节",
            "这是一个测试章节内容",
            [{"id": "test_1", "content": "测试审查细则"}],
            "测试大纲"
        )
        print("✓ 模型服务调用成功")
        print(f"返回结果: {test_result}")

    except Exception as e:
        print(f"✗ 模型服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    # 测试PDF解析
    print("\n3. 测试PDF解析...")
    try:
        pdf_path = "/mnt/d/github/ai-report2/docs/24年部分可研报告样本和评审结果/1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告.pdf"
        print(f"PDF文件路径: {pdf_path}")
        print(f"文件是否存在: {os.path.exists(pdf_path)}")

        if os.path.exists(pdf_path):
            sections = document_parser.parse_pdf(pdf_path)
            print(f"✓ PDF解析成功，提取到 {len(sections)} 个章节")
            for section_name, content in sections.items():
                print(f"  - {section_name}: {len(content)} 字符")
        else:
            print("✗ PDF文件不存在")
            return False

    except Exception as e:
        print(f"✗ PDF解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    print("\n✓ 所有组件测试通过")
    return True

if __name__ == "__main__":
    test_components()
