import unittest
import sys
import os
import json
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.model_service import ModelService
from services.document_parser import DocumentParser
from services.report_analyzer import ReportAnalyzer
from dotenv import load_dotenv

load_dotenv()
model_service = ModelService()

def test_analyze_section():
        # 准备测试数据
        project_name = "广西电网项目"
        section_title = "测试章节"
        section_content = "这是一个测试章节内容，包含一些测试文本用于分析。"
        all_criteria = [
            {
                "id": "CR001",
                "content": "审查细则1: 项目名称应明确具体"
            },
            {
                "id": "CR002",
                "content": "审查细则2: 应包含详细的实施方案"
            }
        ]
        review_guide = "审查指南：请确保项目名称明确具体，并包含详细的实施方案。"

        # 调用被测方法
        result = model_service.analyze_section_batch(
            project_name,
            section_title,
            section_content,
            all_criteria,
            review_guide
        )

        # 验证返回结果是否为JSON格式
        try:
            print(f"审查结果: {result['criteria_results']}")
        except Exception as e:
            raise ValueError(f"返回结果不是有效的JSON格式: {e}")

def test_analyze_single_section(pdf_path, section_title):
        # 初始化服务
        model_service = ModelService()
        document_parser = DocumentParser()
        report_analyzer = ReportAnalyzer(model_service, document_parser)

        # 加载模板文件
        report_analyzer._load_templates()
        print(f"审查细则总数: {len(report_analyzer.criteria)}")

        # 调用被测方法
        section_content,result = report_analyzer.analyze_single_section(pdf_path, section_title)

        print(f"返回结果类型: {type(result)}")
        print(f"返回结果: {result}")

        # 验证返回结果是否为JSON格式
        try:
            # 验证是否包含预期的键
            print(f"！！！审查结果: {result['criteria_results']}")
            print(f"实际返回结果数量: {len(result['criteria_results'])}")
            print(f"期望结果数量: {len(report_analyzer.criteria)}")

            # 检查是否有错误结果
            for i, criterion_result in enumerate(result['criteria_results']):
                criterion_id = criterion_result.get('criterion_id', 'unknown')
                if criterion_id in ['api_error', 'parse_error']:
                    print(f"发现错误结果 {i}: {criterion_result}")

        except Exception as e:
            raise ValueError(f"解析错误: {e}")
def test_analyze_pdf(pdf_path):
        # 初始化服务
        model_service = ModelService()
        document_parser = DocumentParser()
        report_analyzer = ReportAnalyzer(model_service, document_parser)

        # 加载模板文件
        report_analyzer._load_templates()
        print(f"审查细则总数: {len(report_analyzer.criteria)}")

        # 调用被测方法
        result = report_analyzer.analyze(pdf_path)

        print(f"返回结果类型: {type(result)}")
        # 将reult写入当前目录的0report_result.json文件
        with open('0report_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=4)
        #print(f"返回结果: {result}")
def test_prompt(): 
    model_service = ModelService()
    document_parser = DocumentParser()
    report_analyzer = ReportAnalyzer(model_service, document_parser)
    report_analyzer._load_templates()
    outline = report_analyzer.outline
    system_prompt = """
# 角色
你是专业的可研报告评审专家，负责对多个审查细则进行批量全文综合分析。

# 任务
基于编制大纲要求和各章节的初步分析结果，对所有审查细则给出全文综合评审意见。

# 分析要求
1. 综合考虑报告全文内容，不仅仅是单个章节
2. 基于各章节的分析结果，给出每个审查细则的整体符合情况
3. 识别关键发现和问题
4. 提供具体的改进建议

# 输出格式
请严格按照以下JSON格式输出，包含所有审查细则的分析结果：
```json
{
  "criteria_comprehensive_results": [
    {
      "criterion_id": "审查细则ID",
      "comprehensive_analysis": "对该审查细则的全文综合分析，包括整体评价和关键发现",
      "overall_assessment": "符合/基本符合/不符合/不适用",
      "key_findings": [
        "关键发现1",
        "关键发现2"
      ],
      "recommendations": [
        "具体改进建议1",
        "具体改进建议2"
      ]
    }
  ]
}
```

# 评审标准
- **符合**：报告全文完全满足该审查细则要求
- **基本符合**：报告全文大部分满足要求，但有轻微不足
- **不符合**：报告全文明显不满足该审查细则要求
- **不适用**：该审查细则与报告内容无关
- 判定标准：如果一个评审细则的所有章节都不适用，可以检查审查细则和编制大纲要求，查看该审查细则是否必需项，如果是必需项则判定为符合，否则判定为不符合。

# 编制大纲 的内容
""" + f"{outline}"
    print(f"大纲生成:{system_prompt}")

if __name__ == '__main__':
    # test_analyze_single_section("templates/test_可行性研究报告.pdf", "1 概述")
    #test_analyze_pdf("uploads/test_可行性研究报告.pdf")
    test_prompt()