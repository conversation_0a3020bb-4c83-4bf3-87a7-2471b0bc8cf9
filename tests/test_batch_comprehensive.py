#!/usr/bin/env python3
"""
测试批量全文综合分析功能
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.model_service import ModelService
from services.document_parser import DocumentParser
from services.report_analyzer import ReportAnalyzer

def test_batch_comprehensive_analysis():
    """测试批量综合分析方法"""
    print("=== 测试批量综合分析方法 ===")
    
    # 设置测试模式
    os.environ["TEST_MODE"] = "true"
    
    try:
        model_service = ModelService()
        
        # 模拟审查细则数据
        criteria_results = [
            {
                "criterion_id": "1.1",
                "criterion_content": "测试审查细则1内容",
                "section_results": [
                    {
                        "section": "1 概述",
                        "result": "符合",
                        "explanation": "测试说明1"
                    },
                    {
                        "section": "2 项目建设背景",
                        "result": "基本符合",
                        "explanation": "测试说明2"
                    }
                ]
            },
            {
                "criterion_id": "1.2",
                "criterion_content": "测试审查细则2内容",
                "section_results": [
                    {
                        "section": "1 概述",
                        "result": "不符合",
                        "explanation": "测试说明3"
                    }
                ]
            },
            {
                "criterion_id": "2.1",
                "criterion_content": "测试审查细则3内容",
                "section_results": [
                    {
                        "section": "3 项目需求分析",
                        "result": "符合",
                        "explanation": "测试说明4"
                    }
                ]
            }
        ]
        
        # 模拟审查指南和报告摘要
        review_guide = "测试审查指南内容"
        report_summary = "测试报告摘要"
        
        print(f"调用批量综合分析方法，处理 {len(criteria_results)} 个审查细则...")
        
        batch_results = model_service.analyze_criteria_comprehensive_batch(
            review_guide=review_guide,
            criteria_results=criteria_results,
            report_summary=report_summary
        )
        
        print(f"批量综合分析完成，返回 {len(batch_results)} 个结果")
        
        # 检查结果格式
        if len(batch_results) != len(criteria_results):
            print(f"✗ 结果数量不匹配: 期望 {len(criteria_results)}, 实际 {len(batch_results)}")
            return False
        
        for i, result in enumerate(batch_results):
            criterion_id = criteria_results[i]["criterion_id"]
            print(f"\n审查细则 {criterion_id} 的综合分析结果:")
            
            # 检查必要字段
            required_fields = ["comprehensive_analysis", "overall_assessment", "key_findings", "recommendations"]
            for field in required_fields:
                if field in result:
                    print(f"  ✓ {field}: {result[field]}")
                else:
                    print(f"  ✗ 缺少字段: {field}")
                    return False
        
        print("\n✓ 批量综合分析方法测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 批量综合分析方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_analysis_with_batch():
    """测试完整分析流程中的批量综合分析"""
    print("\n=== 测试完整分析流程中的批量综合分析 ===")
    
    # 设置测试模式
    os.environ["TEST_MODE"] = "true"
    
    try:
        model_service = ModelService()
        document_parser = DocumentParser()
        analyzer = ReportAnalyzer(model_service, document_parser)
        
        # 使用测试PDF文件
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        test_pdf = os.path.join(current_dir, "templates/test_可行性研究报告.pdf")
        
        if not os.path.exists(test_pdf):
            print(f"测试PDF文件不存在: {test_pdf}")
            return False
        
        print("开始完整分析流程...")
        result = analyzer.analyze(test_pdf)
        
        print(f"分析完成，结果包含以下字段: {list(result.keys())}")
        
        # 检查是否包含综合分析结果
        if "criteria_analysis" in result:
            criteria_analysis = result["criteria_analysis"]
            print(f"获得 {len(criteria_analysis)} 个审查细则的分析结果")
            
            # 检查每个审查细则是否包含综合分析字段
            comprehensive_count = 0
            for criterion in criteria_analysis:
                if "comprehensive_analysis" in criterion:
                    comprehensive_count += 1
            
            print(f"其中 {comprehensive_count} 个包含综合分析结果")
            
            if comprehensive_count == len(criteria_analysis):
                print("✓ 所有审查细则都包含综合分析结果")
                
                # 显示第一个审查细则的综合分析结果
                if criteria_analysis:
                    first_criterion = criteria_analysis[0]
                    print(f"\n第一个审查细则 {first_criterion.get('criterion_id', 'unknown')} 的综合分析:")
                    print(f"  综合分析: {first_criterion.get('comprehensive_analysis', 'N/A')}")
                    print(f"  整体评估: {first_criterion.get('overall_assessment', 'N/A')}")
                    print(f"  关键发现: {first_criterion.get('key_findings', [])}")
                    print(f"  改进建议: {first_criterion.get('recommendations', [])}")
                
                return True
            else:
                print(f"✗ 只有 {comprehensive_count}/{len(criteria_analysis)} 个审查细则包含综合分析结果")
                return False
        else:
            print("✗ 分析结果中缺少criteria_analysis字段")
            return False
            
    except Exception as e:
        print(f"✗ 完整分析流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """测试性能对比（模拟）"""
    print("\n=== 测试性能对比 ===")
    
    try:
        # 模拟数据
        criteria_count = 5
        
        print(f"模拟处理 {criteria_count} 个审查细则:")
        
        # 原来的方式：逐个调用
        print(f"原来的方式: {criteria_count} 次API调用")
        
        # 新的方式：批量调用
        print(f"优化后的方式: 1 次批量API调用")
        
        # 计算理论性能提升
        api_call_reduction = criteria_count - 1
        efficiency_improvement = (api_call_reduction / criteria_count) * 100
        
        print(f"API调用次数减少: {api_call_reduction} 次")
        print(f"效率提升: {efficiency_improvement:.1f}%")
        
        print("✓ 性能优化显著")
        return True
        
    except Exception as e:
        print(f"✗ 性能对比测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试批量全文综合分析功能...\n")
    
    tests = [
        ("批量综合分析方法", test_batch_comprehensive_analysis),
        ("完整分析流程", test_full_analysis_with_batch),
        ("性能对比", test_performance_comparison)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{test_name}: {'✓ 通过' if result else '✗ 失败'}")
        except Exception as e:
            print(f"\n{test_name}: ✗ 异常 - {e}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有批量综合分析功能测试通过！")
    else:
        print("⚠️  部分批量综合分析功能测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
