from typing import Dict, Any, List, Tuple
from .document_parser import DocumentParser
from .model_service import ModelService
import os

class ReportAnalyzer:
    def __init__(self, model_service: ModelService, document_parser: DocumentParser):
        self.model_service = model_service
        self.document_parser = document_parser
        self.outline = None
        self.criteria = None
        self.review_guide = None

        # 获取当前文件所在目录的父目录（agent目录）
        self.current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def _load_templates(self):
        """延迟加载模板文件"""
        if self.outline is None or self.criteria is None or self.review_guide is None:
            print("加载模板文件...")

            # 解析大纲、审查细则和审查指南
            outline_path = os.path.join(self.current_dir,
                                os.getenv("OUTLINE_FILE", "templates/test_编制大纲.docx"))
            criteria_path = os.path.join(self.current_dir,
                                os.getenv("RULES_FILE", "templates/test_审核表.xlsx"))
            guide_path = os.path.join(self.current_dir,
                                os.getenv("GUIDE_FILE", "templates/可行性研究报告审查指南.docx"))

            print(f"大纲文件路径: {outline_path}")
            print(f"审查细则文件路径: {criteria_path}")
            print(f"审查指南文件路径: {guide_path}")

            self.outline = self.document_parser.parse_outline(outline_path)
            # 附加一个资质证书章节
            self.outline["资质证书"] = ""
            self.criteria = self.document_parser.parse_review_criteria(criteria_path)
            self.review_guide = self.document_parser.parse_review_guide(guide_path)

            print(f"模板文件加载完成: 大纲 {len(self.outline)} 章节, 审查细则 {len(self.criteria)} 项, 审查指南 {len(self.review_guide)} 字符")

    def _extract_section_content_from_pdf(self, outline_title: str, pdf_sections: dict) -> str:
        """从PDF提取的章节中查找对应大纲章节的内容"""

        # 1. 直接匹配章节标题
        if outline_title in pdf_sections:
            return pdf_sections[outline_title]

        # 2. 模糊匹配：提取关键词进行匹配
        outline_keywords = self.document_parser._extract_chapter_keywords(outline_title)

        best_match = ""
        best_score = 0

        for pdf_title, pdf_content in pdf_sections.items():
            pdf_keywords = self.document_parser._extract_chapter_keywords(pdf_title)

            # 计算关键词匹配度
            if outline_keywords and pdf_keywords:
                common_keywords = set(outline_keywords) & set(pdf_keywords)
                score = len(common_keywords) / max(len(outline_keywords), len(pdf_keywords))

                if score > best_score and score > 0.3:  # 至少30%的关键词匹配
                    best_score = score
                    best_match = pdf_content

        if best_match:
            return best_match

        # 3. 如果没有找到匹配的章节，返回空内容（表示该章节在PDF中缺失）
        return f"注意：在可研报告中未找到与大纲章节「{outline_title}」对应的内容。"

    def analyze_single_section(self, pdf_path: str, section_title: str, debug_callback=None) -> Dict[str, Any]:
        """分析单个章节"""
        # 调试信息回调函数
        def debug_log(message, level="info"):
            if debug_callback:
                debug_callback(message, level)
            print(f"[{level.upper()}] {message}")

        # 确保模板已加载
        self._load_templates()
        debug_log("模板文件加载完成")

        # 解析PDF文件
        debug_log(f"开始解析PDF文件: {pdf_path}")
        project_name, pdf_sections = self.document_parser.parse_pdf(pdf_path)
        if not project_name:
            project_name = os.path.splitext(os.path.basename(pdf_path))[0]
        debug_log(f"PDF解析完成，项目名称: {project_name}")

        # 检查章节是否存在
        if section_title not in self.outline:
            available_sections = list(self.outline.keys())
            debug_log(f"章节 '{section_title}' 不存在，可用章节: {available_sections}", "error")
            return {"error": f"章节 '{section_title}' 不存在", "available_sections": available_sections}

        # 分析指定章节
        return self._analyze_section_core(
            project_name, section_title, pdf_sections, debug_callback
        )

    def _analyze_section_core(self, project_name: str, section_title: str, pdf_sections: Dict[str, str], debug_callback=None) -> Tuple[str,Dict[str, Any]]:
        """分析单个章节的核心逻辑（可复用）"""
        def debug_log(message, level="info"):
            if debug_callback:
                debug_callback(message, level)
            print(f"[{level.upper()}] {message}")

        debug_log(f"开始分析章节: {section_title}")

        # 从PDF提取的章节中查找对应的内容
        section_content = self._extract_section_content_from_pdf(section_title, pdf_sections)
        debug_log(f"从PDF中提取到章节内容，长度: {len(section_content)} 字符")

        # 获取该章节对应的大纲要求
        chapter_outline = self.outline[section_title]

        # 使用批量分析方法
        batch_result = self.model_service.analyze_section_batch(
            project_name=project_name,
            section_title=section_title,
            section_content=section_content,
            all_criteria=self.criteria,
            chapter_outline=chapter_outline,
            review_guide=self.review_guide,
            debug_callback=debug_callback
        )
        return section_content, batch_result

    def analyze(self, pdf_path: str, debug_callback=None) -> Dict[str, Any]:
        """分析可研报告"""
        import time

        # 记录开始时间
        start_time = time.time()

        # 重置模型服务的统计信息
        self.model_service.reset_stats()

        # 调试信息回调函数
        def debug_log(message, level="info"):
            if debug_callback:
                debug_callback(message, level)
            print(f"[{level.upper()}] {message}")

        # 确保模板已加载
        self._load_templates()
        debug_log("模板文件加载完成")

        # 解析PDF文件
        debug_log(f"开始解析PDF文件: {pdf_path}")
        project_name, pdf_sections = self.document_parser.parse_pdf(pdf_path)
        # 如果未提取到项目名称，则使用文件名称做为项目名称
        if not project_name:
            project_name = os.path.splitext(os.path.basename(pdf_path))[0]
        debug_log(f"PDF解析完成，项目名称: {project_name}，从PDF提取到 {len(pdf_sections)} 个章节")

        #
        #
        review_results = {
            "project_name": project_name,
            "sections": [],
            "criterion_results": []
        }
        criterion_results = {}
        # 初始化各审查细则结果
        for criterion in self.criteria:
            criterion_results[criterion["id"]] = {
                "criterion_id": criterion["id"],
                "criterion_content": criterion["content"],
                "sections": [],
            }

        total_outline_sections = len(self.outline)
        current_section = 0

        debug_log(f"开始按大纲章节进行分析，共 {total_outline_sections} 个标准章节")

        for outline_title, outline_content in self.outline.items():
            current_section += 1
            debug_log(f"开始分析大纲章节 ({current_section}/{total_outline_sections}): {outline_title}")

            section_content, batch_result = self._analyze_section_core(
                project_name=project_name,
                section_title=outline_title,
                pdf_sections=pdf_sections,
                debug_callback=debug_callback
            )

            # 章节预览
            section_item = {
                "section_name": outline_title,
                "content_length": len(section_content),
                "has_content": bool(section_content.strip()),
                "summary": batch_result.get("summary", "")
            }
            review_results["sections"].append(section_item)

            # 处理批量分析结果
            if "criteria_results" in batch_result:
                for section_result in batch_result["criteria_results"]:
                    criterion_id = section_result.get("criterion_id", "未知")
                    if criterion_id in criterion_results:
                        # 获取该章节此审查细则的审查结果
                        section_criterion = {
                            "section_name": outline_title,
                            "result": section_result.get("result", "未知"),
                            "explanation": section_result.get("explanation", "")
                        }
                        criterion_results[criterion_id]["sections"].append(section_criterion)
            else:
                # 如果批量分析失败，回退到原有方法
                debug_log(f"章节 {outline_title} 批量分析失败，回退到逐个分析模式", "error")
                return {"error": "批量分析失败"}

            debug_log(f"章节 {outline_title} 分析完成", "success")

        # 将审查细则重新以列表方式组织到review_results
        for id in criterion_results:
            result = criterion_results[id]
            review_results["criterion_results"].append(result)

        # 对每个审查细则进行全文综合分析
        print(f"\n开始进行全文综合分析...")

        # 批量进行全文综合分析（一次性处理所有审查细则）
        print(f"开始批量全文综合分析，共 {len(review_results["criterion_results"])} 个审查细则...")

        # 调用批量综合分析
        batch_comprehensive_results = self.model_service.analyze_criteria_comprehensive_batch(
            review_guide=self.review_guide,
            review_results=review_results
        )

        # 将批量综合分析结果合并到criterion_results中
        for i, criterion in enumerate(review_results["criterion_results"]):
            # 获取对应的综合分析结果
            if i < len(batch_comprehensive_results):
                comprehensive_result = batch_comprehensive_results[i]
                criterion.update({
                    "comprehensive_analysis": comprehensive_result.get("comprehensive_analysis", ""),
                    "overall_assessment": comprehensive_result.get("overall_assessment", "不适用"),
                    "key_findings": comprehensive_result.get("key_findings", []),
                    "recommendations": comprehensive_result.get("recommendations", [])
                })
            else:
                # 如果批量分析结果不足，使用默认值
                criterion.update({
                    "comprehensive_analysis": "批量分析结果不足",
                    "overall_assessment": "不适用",
                    "key_findings": ["批量分析异常"],
                    "recommendations": ["建议重新分析"]
                })

        # 生成总体评审意见
        print(f"\n开始生成总体评审意见...")
        summary = self.model_service.summarize_review(review_results)

        # 计算总执行时间
        end_time = time.time()
        total_execution_time = end_time - start_time

        # 获取模型服务的统计信息
        model_stats = self.model_service.get_stats()

        # 构建计时统计信息
        timing_stats = {
            "execution_time": round(total_execution_time, 2),
            "total_input_tokens": model_stats["total_input_tokens"],
            "total_output_tokens": model_stats["total_output_tokens"],
            "total_tokens": model_stats["total_tokens"],
            "api_calls": model_stats["api_calls"],
            "total_api_time": round(model_stats["total_api_time"], 2)
        }

        debug_log(f"评审完成，总耗时: {timing_stats['execution_time']}秒, API调用: {timing_stats['api_calls']}次, 总Token: {timing_stats['total_tokens']}", "success")

        debug_log(f"数据结构已按照审查细则组织，共 {len(review_results['sections'])} 个章节，{len(review_results['criterion_results'])} 个审查细则", "success")

        return {
            "sections": review_results["sections"],
            "review_results": review_results["criterion_results"],
            "summary": summary["summary"],
            "statistics": self._generate_statistics_from_criteria(review_results["criterion_results"]),
            "timing_stats": timing_stats,
            # 保留原有字段以确保向后兼容
            "criteria_analysis": review_results["criterion_results"]
        }

    def _generate_statistics(self, review_results: list) -> Dict[str, Any]:
        """生成评审统计信息"""
        total_criteria = 0
        result_counts = {"符合": 0, "基本符合": 0, "不符合": 0, "不适用": 0}

        for section in review_results:
            for analysis in section["analysis"]:
                total_criteria += 1
                result = analysis.get("result", "不适用")
                if result in result_counts:
                    result_counts[result] += 1
                else:
                    result_counts["不适用"] += 1

        return {
            "total_criteria": total_criteria,
            "total_sections": len(review_results),
            "result_distribution": result_counts,
            "compliance_rate": round((result_counts["符合"] + result_counts["基本符合"]) / max(total_criteria, 1) * 100, 2) if total_criteria > 0 else 0
        }

    def _generate_statistics_from_criteria(self, criteria_results: list) -> Dict[str, Any]:
        """从按审查细则组织的数据生成评审统计信息"""
        total_criteria = len(criteria_results)
        result_counts = {"符合": 0, "基本符合": 0, "不符合": 0, "不适用": 0}

        for criterion in criteria_results:
            # 使用overall_assessment或overall_result作为统计依据
            result = criterion.get("overall_assessment") or criterion.get("overall_result", "不适用")
            if result in result_counts:
                result_counts[result] += 1
            else:
                result_counts["不适用"] += 1

        return {
            "total_criteria": total_criteria,
            "total_sections": 0,  # 这里不统计章节数，因为数据结构不同
            "result_distribution": result_counts,
            "compliance_rate": round((result_counts["符合"] + result_counts["基本符合"]) / max(total_criteria, 1) * 100, 2) if total_criteria > 0 else 0
        }

    def _organize_by_criteria(self, review_results: list) -> List[Dict[str, Any]]:
        """按审查细则重新组织评审结果"""
        criteria_map = {}

        # 遍历所有章节的评审结果，按审查细则ID分组
        for section in review_results:
            section_name = section.get("section", "未知章节")
            section_has_content = section.get("has_content", False)

            for analysis in section.get("analysis", []):
                criterion_id = analysis.get("criterion_id", "unknown")
                criterion_content = analysis.get("criterion_content", "")
                result = analysis.get("result", "不适用")
                explanation = analysis.get("explanation", "")

                # 跳过无效ID的审查细则（但保留行号生成的ID）
                if criterion_id in ["unknown", "parse_error", "api_error"]:
                    print(f"跳过无效的审查细则ID: '{criterion_id}'")
                    continue

                # 如果这个审查细则还没有记录，创建新记录
                if criterion_id not in criteria_map:
                    criteria_map[criterion_id] = {
                        "criterion_id": criterion_id,
                        "criterion_content": criterion_content,
                        "section_results": [],
                        "overall_result": "不适用",
                        "compliance_sections": [],
                        "non_compliance_sections": [],
                        "not_applicable_sections": [],
                        "improvement_suggestions": []
                    }

                # 检查是否已经存在相同的章节结果（去重）
                existing_sections = [sr["section"] for sr in criteria_map[criterion_id]["section_results"]]
                if section_name in existing_sections:
                    print(f"跳过重复的章节结果: 审查细则{criterion_id} - {section_name}")
                    continue

                # 添加该章节对此审查细则的评审结果
                criteria_map[criterion_id]["section_results"].append({
                    "section": section_name,
                    "has_content": section_has_content,
                    "result": result,
                    "explanation": explanation
                })

                # 根据结果分类章节（也需要去重）
                if result == "符合":
                    if not any(cs["section"] == section_name for cs in criteria_map[criterion_id]["compliance_sections"]):
                        criteria_map[criterion_id]["compliance_sections"].append({
                            "section": section_name,
                            "explanation": explanation
                        })
                elif result == "基本符合":
                    if not any(cs["section"] == section_name for cs in criteria_map[criterion_id]["compliance_sections"]):
                        criteria_map[criterion_id]["compliance_sections"].append({
                            "section": section_name,
                            "explanation": explanation
                        })
                elif result == "不符合":
                    if not any(ncs["section"] == section_name for ncs in criteria_map[criterion_id]["non_compliance_sections"]):
                        criteria_map[criterion_id]["non_compliance_sections"].append({
                            "section": section_name,
                            "explanation": explanation
                        })
                else:  # 不适用
                    if not any(nas["section"] == section_name for nas in criteria_map[criterion_id]["not_applicable_sections"]):
                        criteria_map[criterion_id]["not_applicable_sections"].append({
                            "section": section_name,
                            "explanation": explanation
                        })

        # 为每个审查细则确定总体结果和改进建议
        for criterion_id, criterion_data in criteria_map.items():
            criterion_data["overall_result"] = self._determine_overall_result(criterion_data)
            criterion_data["improvement_suggestions"] = self._generate_improvement_suggestions(criterion_data)

        # 按审查细则ID排序并返回
        sorted_criteria = sorted(criteria_map.values(), key=lambda x: self._sort_criterion_id(x["criterion_id"]))
        return sorted_criteria

    def _determine_overall_result(self, criterion_data: Dict[str, Any]) -> str:
        """确定审查细则的总体评审结果"""
        compliance_count = len(criterion_data["compliance_sections"])
        non_compliance_count = len(criterion_data["non_compliance_sections"])

        # 如果有不符合的章节，总体结果为不符合
        if non_compliance_count > 0:
            return "不符合"
        # 如果有符合的章节，总体结果为符合或基本符合
        elif compliance_count > 0:
            # 检查是否所有有内容的章节都符合
            total_content_sections = sum(1 for result in criterion_data["section_results"]
                                       if result["has_content"] and result["result"] != "不适用")
            if total_content_sections > 0 and compliance_count == total_content_sections:
                return "符合"
            else:
                return "基本符合"
        # 如果所有章节都不适用
        else:
            return "不适用"

    def _generate_improvement_suggestions(self, criterion_data: Dict[str, Any]) -> List[str]:
        """为审查细则生成改进建议"""
        suggestions = []

        # 针对不符合的章节生成建议
        for non_compliance in criterion_data["non_compliance_sections"]:
            if non_compliance["explanation"] and non_compliance["explanation"] != "不适用":
                suggestions.append(f"在{non_compliance['section']}中：{non_compliance['explanation']}")

        # 如果没有具体的不符合项，但总体结果不是完全符合，给出通用建议
        if not suggestions and criterion_data["overall_result"] in ["不符合", "基本符合"]:
            suggestions.append(f"建议在相关章节中补充完善{criterion_data['criterion_content'][:50]}...的相关内容")

        return suggestions

    def _sort_criterion_id(self, criterion_id: str) -> tuple:
        """审查细则ID排序辅助函数"""
        try:
            # 尝试提取数字部分进行排序
            if criterion_id and criterion_id.replace(".", "").isdigit():
                return (float(criterion_id), "")
            else:
                return (999, criterion_id)  # 非数字ID排在后面
        except:
            return (999, criterion_id)

    def _generate_section_summary(self, section: Dict[str, Any]) -> str:
        """生成章节内容摘要"""
        if not section.get("has_content", False):
            return "该章节在可研报告中无相关内容"

        # 统计该章节的评审结果
        analysis = section.get("analysis", [])
        if not analysis:
            return "该章节未进行评审分析"

        result_counts = {"符合": 0, "基本符合": 0, "不符合": 0, "不适用": 0}
        for item in analysis:
            result = item.get("result", "不适用")
            if result in result_counts:
                result_counts[result] += 1
            else:
                result_counts["不适用"] += 1

        # 生成摘要文本
        total_applicable = result_counts["符合"] + result_counts["基本符合"] + result_counts["不符合"]
        if total_applicable == 0:
            return "该章节的所有审查细则均不适用"

        compliant = result_counts["符合"] + result_counts["基本符合"]
        summary = f"该章节共涉及{total_applicable}个审查细则，其中{compliant}个符合要求"

        if result_counts["不符合"] > 0:
            summary += f"，{result_counts['不符合']}个不符合要求"

        return summary + "。"

    def _organize_review_results_by_criteria(self, enhanced_criteria: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按照参考格式重新组织审查细则结果"""
        organized_results = []

        for criterion in enhanced_criteria:
            # 构建章节评审结果列表
            sections_results = []
            for section_result in criterion.get("section_results", []):
                section_info = {
                    "section_name": section_result.get("section", ""),
                    "result": section_result.get("result", "不适用"),
                    "explanation": section_result.get("explanation", "")
                }
                sections_results.append(section_info)

            # 构建按照参考格式的审查细则结果
            organized_criterion = {
                "criterion_id": criterion.get("criterion_id", ""),
                "criterion_content": criterion.get("criterion_content", ""),
                "sections": sections_results
            }

            organized_results.append(organized_criterion)

        return organized_results