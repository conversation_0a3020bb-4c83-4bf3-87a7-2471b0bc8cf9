from fastapi import FastAP<PERSON>, UploadFile, File
from fastapi.responses import JSONResponse, HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi import Request
import uvicorn
from services.report_analyzer import ReportAnalyzer
from services.document_parser import DocumentParser
from services.model_service import ModelService
import os
import json
import asyncio
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(title="可研报告评审助手")
# 设置模板目录
current_dir = os.path.dirname(os.path.abspath(__file__))
templates = Jinja2Templates(directory=os.path.join(current_dir, "templates"))
# 新增静态文件挂载
app.mount("/static", StaticFiles(directory=os.path.join(current_dir, "templates")), name="static")

# 初始化服务
model_service = ModelService()
document_parser = DocumentParser()
report_analyzer = ReportAnalyzer(model_service, document_parser)

# 全局调试信息存储
debug_messages = []
debug_clients = []

def add_debug_message(message: str, level: str = "info"):
    """添加调试信息"""
    return
    import time
    debug_msg = {
        "timestamp": time.time(),
        "message": message,
        "level": level
    }
    debug_messages.append(debug_msg)

    # 只保留最近100条消息
    if len(debug_messages) > 100:
        debug_messages.pop(0)

    # 通知所有连接的客户端
    for client_queue in debug_clients:
        try:
            client_queue.put_nowait(debug_msg)
        except:
            pass


@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/debug-stream")
async def debug_stream():
    """SSE端点，用于实时推送调试信息"""
    import queue
    import time

    client_queue = queue.Queue()
    debug_clients.append(client_queue)

    async def event_generator():
        try:
            # 发送历史消息
            for msg in debug_messages[-10:]:  # 只发送最近10条
                yield f"data: {json.dumps(msg)}\n\n"

            # 持续发送新消息
            while True:
                try:
                    # 等待新消息，超时1秒
                    msg = client_queue.get(timeout=1)
                    yield f"data: {json.dumps(msg)}\n\n"
                except queue.Empty:
                    # 发送心跳
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"
                except Exception as e:
                    print(f"SSE error: {e}")
                    break
        finally:
            # 清理客户端连接
            if client_queue in debug_clients:
                debug_clients.remove(client_queue)

    return StreamingResponse(
        event_generator(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@app.post("/analyze")
async def analyze_report(pdf_file: UploadFile = File(...)):
    try:
        add_debug_message(f"收到文件: {pdf_file.filename}", "info")

        # 获取当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        uploads_dir = os.path.join(current_dir, "uploads")

        # 保存上传的PDF文件
        pdf_path = os.path.join(uploads_dir, pdf_file.filename)
        os.makedirs(uploads_dir, exist_ok=True)
        with open(pdf_path, "wb") as f:
            content = await pdf_file.read()
            f.write(content)

        add_debug_message(f"文件已保存到: {pdf_path}", "success")

        # 分析报告
        add_debug_message("开始分析报告...", "info")
        result = report_analyzer.analyze(pdf_path, debug_callback=add_debug_message)
        add_debug_message("分析完成", "success")

        return JSONResponse(content=result)
    except Exception as e:
        error_msg = f"错误: {str(e)}"
        add_debug_message(error_msg, "error")
        print(error_msg)
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

if __name__ == "__main__":
    import sys
    port = 8000
    if len(sys.argv) > 2 and sys.argv[1] == "--port":
        port = int(sys.argv[2])
    uvicorn.run(app, host="0.0.0.0", port=port)