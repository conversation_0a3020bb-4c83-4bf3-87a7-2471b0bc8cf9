# 需求
使用python实现一个ai可研报告评审助手，根据《农村电网巩固提升工程中央预算内投资项目可行性研究报告编制和审查指南》对用户上传的PDF文档进行评审，按照《中央预算投资项目审核表》中的评审细则，逐条审查，给出输出审查情况：（符合、基本符合、不符合），不符合情况给出具体原因。
# 功能
## 可行性研究 告编制大纲解析
读取word文档《农村电网巩固提升工程中央预算内投资项目可行性研究报告编制和审查指南》，解析得到《可行性研究 报告编制大纲》用于后续的审查。
## 审查细则解析
读取excel文件《中央预算投资项目审核表》，解析得到各个审查细则。
## pdf文件解析
 读取用户上传的pdf文件，识别章节、按章节提取其中的文本内容。
## 逐条审查
 调用后台大模型服务，逐个章节内容进行审查，首先要检查章节内容是否与审查细则相关，如相关则并给出审查情况，以第1章节为例，对于"审查细则1"可能输出内容包括：
  - 当与章节1与"审查项1"不相关时，可输出：不适用该审查项
  - 当与章节1与"审查项1"相关时，可输出：具体的审查情况，如
    * 或 章节1 基本符合
    * 或 章节1 改动了大纲标题：1.2
    * 或 章节1 投资估算编制说明应包括工程量确定的主要依据和计算原则
  - 检查章节内容时，也需要对比大纲中相应章节的要求。
  - 根据需求，要生成调用大模型服务的合适的系统提示词。
## 审查情况汇总
当所有章节对都审查完毕，则再次调用大模型服务，检查汇总各审查细则，输出各审查细则的审查情况，如：
 - 基本符合 
 - 改动了标题：1.2
 - 根据需求，要生成调用大模型服务的合适的系统提示词。

## 大模型服务
兼容openai接口，可调用本地部署的大模型服务，返回结果。

## 可研报告大纲
1 概述 
2 项目建设背景和必要性
3 项目需求分析与预期产出
4 项目选址与要素保障
5 项目建设方案
6 项目运营方案
7 项目投融资与财务方案
8 项目影响效果分析
9 项目风险管控方案
10 研究结论及建议
11 附表